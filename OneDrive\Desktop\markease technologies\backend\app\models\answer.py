from app import db
from datetime import datetime

class Answer(db.Model):
    """Answer model for storing student answers and their marks."""
    __tablename__ = 'answers'
    
    id = db.Column(db.Integer, primary_key=True)
    student_answer = db.Column(db.Text)  # The actual answer provided by student
    marks_obtained = db.Column(db.Float, default=0.0)
    is_correct = db.Column(db.<PERSON>, default=False)
    confidence_score = db.Column(db.Float)  # OCR/AI confidence in the answer
    manual_review = db.Column(db.<PERSON><PERSON>, default=False)  # Whether manually reviewed
    reviewer_notes = db.Column(db.Text)  # Notes from manual review
    auto_marked = db.Column(db.<PERSON><PERSON><PERSON>, default=True)  # Whether automatically marked
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Foreign Keys
    question_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('questions.id'), nullable=False)
    answer_sheet_id = db.Column(db.Integer, db.Foreign<PERSON>ey('answer_sheets.id'), nullable=False)
    
    def __init__(self, student_answer, question_id, answer_sheet_id, 
                 marks_obtained=0.0, confidence_score=None):
        self.student_answer = student_answer
        self.question_id = question_id
        self.answer_sheet_id = answer_sheet_id
        self.marks_obtained = marks_obtained
        self.confidence_score = confidence_score
    
    def mark_answer(self, marks, is_correct=None, notes=None, manual=False):
        """Mark the answer with given marks."""
        self.marks_obtained = marks
        if is_correct is not None:
            self.is_correct = is_correct
        if notes:
            self.reviewer_notes = notes
        if manual:
            self.manual_review = True
            self.auto_marked = False
        self.updated_at = datetime.utcnow()
    
    def get_percentage_score(self):
        """Get percentage score for this answer."""
        if self.question and self.question.marks > 0:
            return (self.marks_obtained / self.question.marks) * 100
        return 0.0
    
    def to_dict(self):
        """Convert answer object to dictionary."""
        return {
            'id': self.id,
            'student_answer': self.student_answer,
            'marks_obtained': self.marks_obtained,
            'total_marks': self.question.marks if self.question else 0,
            'percentage_score': round(self.get_percentage_score(), 2),
            'is_correct': self.is_correct,
            'confidence_score': self.confidence_score,
            'manual_review': self.manual_review,
            'reviewer_notes': self.reviewer_notes,
            'auto_marked': self.auto_marked,
            'question_id': self.question_id,
            'question_number': self.question.question_number if self.question else None,
            'question_text': self.question.question_text if self.question else None,
            'question_type': self.question.question_type.value if self.question else None,
            'answer_sheet_id': self.answer_sheet_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Answer Q{self.question.question_number if self.question else "?"}: {self.marks_obtained}/{self.question.marks if self.question else "?"}>'
