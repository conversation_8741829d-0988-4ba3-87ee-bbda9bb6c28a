from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from app import db
from app.models.user import User
from app.models.answer_sheet import AnswerSheet, ProcessingStatus
from app.models.marking_guide import MarkingGuide
from app.models.question import Question
from app.models.answer import Answer
from app.services.ocr_service import OCRService
from app.services.marking_service import MarkingService
import os
import uuid
from datetime import datetime
import threading

answer_sheets_bp = Blueprint('answer_sheets', __name__)

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def process_answer_sheet_async(answer_sheet_id):
    """Process answer sheet asynchronously."""
    try:
        with current_app.app_context():
            answer_sheet = AnswerSheet.query.get(answer_sheet_id)
            if not answer_sheet:
                return
            
            # Update status to processing
            answer_sheet.update_processing_status(ProcessingStatus.PROCESSING)
            db.session.commit()
            
            # Initialize services
            ocr_service = OCRService(current_app.config.get('TESSERACT_CMD'))
            marking_service = MarkingService()
            
            # Process OCR
            ocr_result = ocr_service.process_answer_sheet(answer_sheet.file_path)
            answer_sheet.ocr_text = ocr_result['full_text']
            
            if ocr_result['processing_status'] == 'error':
                answer_sheet.update_processing_status(
                    ProcessingStatus.ERROR, 
                    f"OCR Error: {ocr_result.get('error_message', 'Unknown error')}"
                )
                db.session.commit()
                return
            
            # Update status to processed
            answer_sheet.update_processing_status(ProcessingStatus.PROCESSED)
            
            # Get questions for this marking guide
            questions = Question.query.filter_by(
                marking_guide_id=answer_sheet.marking_guide_id
            ).order_by(Question.question_number).all()
            
            # Extract answers from OCR text (basic implementation)
            # This is a simplified approach - in practice, you'd need more sophisticated
            # answer extraction based on the answer sheet format
            ocr_lines = ocr_result['full_text'].split('\n')
            answer_lines = [line.strip() for line in ocr_lines if line.strip()]
            
            # Mark each question
            for i, question in enumerate(questions):
                # Get student answer (simplified - assumes one line per answer)
                student_answer = ""
                if i < len(answer_lines):
                    student_answer = answer_lines[i]
                
                # Mark the answer
                marking_result = marking_service.mark_question(question, student_answer)
                
                # Create answer record
                answer = Answer(
                    student_answer=student_answer,
                    question_id=question.id,
                    answer_sheet_id=answer_sheet.id,
                    marks_obtained=marking_result['marks_obtained'],
                    confidence_score=marking_result['confidence']
                )
                
                answer.is_correct = marking_result['is_correct']
                answer.reviewer_notes = marking_result['feedback']
                
                if marking_result.get('requires_manual_review'):
                    answer.manual_review = True
                    answer.auto_marked = False
                
                db.session.add(answer)
            
            # Calculate total scores
            answer_sheet.calculate_scores()
            answer_sheet.update_processing_status(ProcessingStatus.MARKED)
            
            db.session.commit()
            
    except Exception as e:
        with current_app.app_context():
            answer_sheet = AnswerSheet.query.get(answer_sheet_id)
            if answer_sheet:
                answer_sheet.update_processing_status(
                    ProcessingStatus.ERROR, 
                    f"Processing Error: {str(e)}"
                )
                db.session.commit()

@answer_sheets_bp.route('', methods=['GET'])
@answer_sheets_bp.route('/', methods=['GET'])
@jwt_required()
def get_answer_sheets():
    """Get all answer sheets for the current teacher."""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status = request.args.get('status')
        marking_guide_id = request.args.get('marking_guide_id', type=int)
        student_name = request.args.get('student_name')
        
        # Build query
        query = AnswerSheet.query.filter_by(teacher_id=user_id)
        
        if status:
            try:
                status_enum = ProcessingStatus(status)
                query = query.filter_by(processing_status=status_enum)
            except ValueError:
                return jsonify({'error': 'Invalid status'}), 400
        
        if marking_guide_id:
            query = query.filter_by(marking_guide_id=marking_guide_id)
        
        if student_name:
            query = query.filter(AnswerSheet.student_name.ilike(f'%{student_name}%'))
        
        # Order by creation date (newest first)
        query = query.order_by(AnswerSheet.created_at.desc())
        
        # Paginate
        answer_sheets = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'answer_sheets': [sheet.to_dict() for sheet in answer_sheets.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': answer_sheets.total,
                'pages': answer_sheets.pages,
                'has_next': answer_sheets.has_next,
                'has_prev': answer_sheets.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@answer_sheets_bp.route('/upload', methods=['POST'])
@jwt_required()
def upload_answer_sheet():
    """Upload and process an answer sheet."""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not allowed'}), 400

        # Get form data
        student_name = request.form.get('student_name')
        marking_guide_id = request.form.get('marking_guide_id', type=int)

        if not student_name or not marking_guide_id:
            return jsonify({'error': 'Student name and marking guide ID are required'}), 400

        # Verify marking guide exists and belongs to teacher
        marking_guide = MarkingGuide.query.filter_by(
            id=marking_guide_id, teacher_id=user_id
        ).first()

        if not marking_guide:
            return jsonify({'error': 'Marking guide not found'}), 404

        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)

        # Save file
        file.save(file_path)
        file_size = os.path.getsize(file_path)

        # Create answer sheet record
        answer_sheet = AnswerSheet(
            student_name=student_name,
            file_path=file_path,
            original_filename=filename,
            teacher_id=user_id,
            marking_guide_id=marking_guide_id,
            student_id=request.form.get('student_id'),
            class_name=request.form.get('class_name'),
            file_size=file_size
        )

        # Parse exam date if provided
        exam_date_str = request.form.get('exam_date')
        if exam_date_str:
            try:
                answer_sheet.exam_date = datetime.strptime(exam_date_str, '%Y-%m-%d').date()
            except ValueError:
                pass  # Invalid date format, ignore

        db.session.add(answer_sheet)
        db.session.commit()

        # Start async processing
        thread = threading.Thread(
            target=process_answer_sheet_async,
            args=(answer_sheet.id,)
        )
        thread.start()

        return jsonify({
            'message': 'Answer sheet uploaded successfully and processing started',
            'answer_sheet': answer_sheet.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@answer_sheets_bp.route('/<int:sheet_id>', methods=['GET'])
@jwt_required()
def get_answer_sheet(sheet_id):
    """Get a specific answer sheet with its answers."""
    try:
        user_id = int(get_jwt_identity())

        answer_sheet = AnswerSheet.query.filter_by(
            id=sheet_id, teacher_id=user_id
        ).first()

        if not answer_sheet:
            return jsonify({'error': 'Answer sheet not found'}), 404

        # Get answers for this sheet
        answers = Answer.query.filter_by(
            answer_sheet_id=sheet_id
        ).join(Question).order_by(Question.question_number).all()

        sheet_dict = answer_sheet.to_dict()
        sheet_dict['answers'] = [answer.to_dict() for answer in answers]

        return jsonify({
            'answer_sheet': sheet_dict
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@answer_sheets_bp.route('/<int:sheet_id>/answers/<int:answer_id>', methods=['PUT'])
@jwt_required()
def update_answer(sheet_id, answer_id):
    """Update a specific answer (for manual review)."""
    try:
        user_id = int(get_jwt_identity())

        # Verify answer sheet belongs to teacher
        answer_sheet = AnswerSheet.query.filter_by(
            id=sheet_id, teacher_id=user_id
        ).first()

        if not answer_sheet:
            return jsonify({'error': 'Answer sheet not found'}), 404

        # Get the answer
        answer = Answer.query.filter_by(
            id=answer_id, answer_sheet_id=sheet_id
        ).first()

        if not answer:
            return jsonify({'error': 'Answer not found'}), 404

        data = request.get_json()

        # Update answer
        if 'marks_obtained' in data:
            marks_obtained = float(data['marks_obtained'])
            max_marks = answer.question.marks if answer.question else 0

            if marks_obtained < 0 or marks_obtained > max_marks:
                return jsonify({'error': f'Marks must be between 0 and {max_marks}'}), 400

            answer.mark_answer(
                marks=marks_obtained,
                is_correct=marks_obtained >= (max_marks * 0.7),
                notes=data.get('reviewer_notes'),
                manual=True
            )

        db.session.commit()

        # Recalculate answer sheet scores
        answer_sheet.calculate_scores()
        db.session.commit()

        return jsonify({
            'message': 'Answer updated successfully',
            'answer': answer.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
