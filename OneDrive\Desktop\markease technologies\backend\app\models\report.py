from app import db
from datetime import datetime
from enum import Enum
import json

class ReportType(Enum):
    INDIVIDUAL = "individual"
    CLASS_SUMMARY = "class_summary"
    SUBJECT_ANALYSIS = "subject_analysis"

class Report(db.Model):
    """Report model for storing generated reports."""
    __tablename__ = 'reports'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    report_type = db.Column(db.Enum(ReportType), nullable=False)
    report_data = db.Column(db.Text)  # JSON string containing report data
    file_path = db.Column(db.String(255))  # Path to generated report file (PDF/Excel)
    summary = db.Column(db.Text)  # Brief summary of the report
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Foreign Keys
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    answer_sheet_id = db.Column(db.Integer, db.ForeignKey('answer_sheets.id'), nullable=True)  # For individual reports
    
    def __init__(self, title, report_type, teacher_id, report_data=None, 
                 answer_sheet_id=None, summary=None):
        self.title = title
        self.report_type = report_type
        self.teacher_id = teacher_id
        self.answer_sheet_id = answer_sheet_id
        self.summary = summary
        if report_data:
            self.set_report_data(report_data)
    
    def get_report_data(self):
        """Get report data as a dictionary."""
        if self.report_data:
            try:
                return json.loads(self.report_data)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_report_data(self, data):
        """Set report data from a dictionary."""
        if data:
            self.report_data = json.dumps(data, default=str)
        else:
            self.report_data = None
    
    def to_dict(self):
        """Convert report object to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'report_type': self.report_type.value,
            'report_data': self.get_report_data(),
            'file_path': self.file_path,
            'summary': self.summary,
            'teacher_id': self.teacher_id,
            'teacher_name': self.teacher.full_name if self.teacher else None,
            'answer_sheet_id': self.answer_sheet_id,
            'student_name': self.answer_sheet.student_name if self.answer_sheet else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Report {self.title} ({self.report_type.value})>'
