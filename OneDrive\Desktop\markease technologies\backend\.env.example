# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database Configuration
DEV_DATABASE_URL=mysql+pymysql://username:password@localhost/markease_dev
TEST_DATABASE_URL=mysql+pymysql://username:password@localhost/markease_test
DATABASE_URL=mysql+pymysql://username:password@localhost/markease

# OCR Configuration
TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
