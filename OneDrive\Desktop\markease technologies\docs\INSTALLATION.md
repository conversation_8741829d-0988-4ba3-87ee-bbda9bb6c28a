# MarkEase Installation Guide

This guide will help you set up the MarkEase automated answer sheet marking system on your local machine.

## Prerequisites

Before installing MarkEase, ensure you have the following installed:

### Required Software

1. **Python 3.8+**
   - Download from [python.org](https://www.python.org/downloads/)
   - Verify installation: `python --version`

2. **MySQL 8.0+**
   - Download from [mysql.com](https://dev.mysql.com/downloads/mysql/)
   - Verify installation: `mysql --version`

3. **Tesseract OCR**
   - **Windows**: Download from [GitHub](https://github.com/UB-Mannheim/tesseract/wiki)
   - **macOS**: `brew install tesseract`
   - **Ubuntu/Debian**: `sudo apt install tesseract-ocr`
   - **CentOS/RHEL**: `sudo yum install tesseract`

### Optional but Recommended

- **Git** for version control
- **Node.js** for frontend build tools (if you plan to modify the frontend)

## Installation Methods

### Method 1: Automated Setup (Recommended)

1. **<PERSON>lone or download the project**
   ```bash
   git clone <repository-url>
   cd markease
   ```

2. **Run the setup script**
   ```bash
   python setup.py
   ```

3. **Follow the prompts** to configure your database connection

4. **Install Tesseract OCR** (if not already installed)

5. **Update the Tesseract path** in `backend/.env` if needed

### Method 2: Manual Setup

#### Step 1: Set up Python Environment

1. **Create virtual environment**
   ```bash
   python -m venv venv
   ```

2. **Activate virtual environment**
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`

3. **Install Python dependencies**
   ```bash
   pip install -r backend/requirements.txt
   ```

#### Step 2: Set up Database

1. **Start MySQL service**
   ```bash
   # Windows (as Administrator)
   net start mysql
   
   # macOS
   brew services start mysql
   
   # Linux
   sudo systemctl start mysql
   ```

2. **Create databases**
   ```sql
   mysql -u root -p
   CREATE DATABASE markease_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE DATABASE markease_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE DATABASE markease CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **Import database schema**
   ```bash
   mysql -u root -p markease_dev < database/schema.sql
   ```

#### Step 3: Configure Environment

1. **Copy environment file**
   ```bash
   cp backend/.env.example backend/.env
   ```

2. **Edit configuration** in `backend/.env`:
   ```env
   # Update with your database credentials
   DEV_DATABASE_URL=mysql+pymysql://username:password@localhost/markease_dev
   
   # Update with your Tesseract path
   TESSERACT_CMD=C:\Program Files\Tesseract-OCR\tesseract.exe  # Windows
   # TESSERACT_CMD=/usr/bin/tesseract  # Linux
   # TESSERACT_CMD=/opt/homebrew/bin/tesseract  # macOS with Homebrew
   ```

3. **Create uploads directory**
   ```bash
   mkdir -p backend/uploads
   ```

## Running the Application

### Start the Backend Server

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Activate virtual environment** (if not already active)
   - Windows: `..\venv\Scripts\activate`
   - macOS/Linux: `source ../venv/bin/activate`

3. **Initialize database** (first time only)
   ```bash
   python app.py init-db
   ```

4. **Start the server**
   ```bash
   python app.py
   ```

   The backend API will be available at `http://localhost:5000`

### Access the Frontend

1. **Open the frontend** in your web browser:
   ```
   file:///path/to/markease/frontend/pages/index.html
   ```

   Or use a local web server:
   ```bash
   # Using Python
   cd frontend
   python -m http.server 8000
   # Then visit http://localhost:8000/pages/
   
   # Using Node.js (if installed)
   npx serve frontend
   ```

## First Time Setup

1. **Register a teacher account**
   - Click "Register" on the homepage
   - Fill in your details
   - Use a strong password

2. **Create your first marking guide**
   - Navigate to "Marking Guides"
   - Click "Create New Guide"
   - Add questions with correct answers

3. **Upload an answer sheet**
   - Go to "Answer Sheets"
   - Click "Upload Answer Sheet"
   - Select your marking guide and upload a scanned answer sheet

## Troubleshooting

### Common Issues

#### Database Connection Error
```
Error: Can't connect to MySQL server
```
**Solution**: 
- Ensure MySQL is running
- Check database credentials in `.env`
- Verify database exists

#### Tesseract Not Found
```
Error: Tesseract not found
```
**Solution**:
- Install Tesseract OCR
- Update `TESSERACT_CMD` path in `.env`
- Restart the backend server

#### File Upload Error
```
Error: File too large
```
**Solution**:
- Check file size (max 16MB by default)
- Ensure `uploads` directory exists and is writable

#### OCR Poor Accuracy
**Solutions**:
- Ensure answer sheets are high quality scans
- Use good lighting when scanning
- Avoid skewed or rotated images
- Consider preprocessing images before upload

### Getting Help

1. **Check the logs** in the backend console for detailed error messages
2. **Verify all prerequisites** are installed correctly
3. **Check file permissions** for the uploads directory
4. **Ensure ports 5000 and 8000** are not in use by other applications

### Development Mode

For development, you can enable debug mode:

1. **Set environment variable**
   ```bash
   export FLASK_ENV=development  # Linux/macOS
   set FLASK_ENV=development     # Windows
   ```

2. **Enable auto-reload**
   ```bash
   python app.py --debug
   ```

## Next Steps

- Read the [User Guide](USER_GUIDE.md) to learn how to use MarkEase
- Check the [API Documentation](API.md) if you plan to integrate with other systems
- See [CONTRIBUTING.md](CONTRIBUTING.md) if you want to contribute to the project

## System Requirements

### Minimum Requirements
- **CPU**: 2 cores, 2.0 GHz
- **RAM**: 4 GB
- **Storage**: 2 GB free space
- **OS**: Windows 10, macOS 10.14, or Ubuntu 18.04+

### Recommended Requirements
- **CPU**: 4 cores, 2.5 GHz+
- **RAM**: 8 GB+
- **Storage**: 10 GB+ free space (for answer sheet storage)
- **OS**: Latest versions of supported operating systems

### Network Requirements
- Internet connection for initial setup and updates
- Local network access if deploying for multiple users
