// Dashboard module for MarkEase application

const Dashboard = {
    // Initialize dashboard
    init() {
        this.setupEventListeners();
    },

    // Setup event listeners
    setupEventListeners() {
        // Quick action buttons
        document.getElementById('upload-sheet-btn')?.addEventListener('click', () => {
            this.showUploadModal();
        });

        document.getElementById('create-guide-btn')?.addEventListener('click', () => {
            App.showMarkingGuides();
        });

        document.getElementById('view-reports-btn')?.addEventListener('click', () => {
            App.showReports();
        });
    },

    // Load dashboard data
    async loadDashboardData() {
        try {
            // Load stats
            await this.loadStats();
            
            // Load recent answer sheets
            await this.loadRecentSheets();
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            showToast('Error loading dashboard data', 'error');
        }
    },

    // Load dashboard statistics
    async loadStats() {
        try {
            // Get answer sheets
            const sheetsResponse = await api.getAnswerSheets({ per_page: 1 });
            document.getElementById('total-sheets').textContent = sheetsResponse.pagination?.total || 0;

            // Get processing sheets
            const processingResponse = await api.getAnswerSheets({ status: 'processing', per_page: 1 });
            document.getElementById('processing-sheets').textContent = processingResponse.pagination?.total || 0;

            // Get marking guides
            const guidesResponse = await api.getMarkingGuides({ per_page: 1 });
            document.getElementById('total-guides').textContent = guidesResponse.pagination?.total || 0;

            // Get reports
            const reportsResponse = await api.getReports({ per_page: 1 });
            document.getElementById('total-reports').textContent = reportsResponse.pagination?.total || 0;

        } catch (error) {
            console.error('Error loading stats:', error);
        }
    },

    // Load recent answer sheets
    async loadRecentSheets() {
        try {
            const response = await api.getAnswerSheets({ per_page: 5 });
            const recentSheetsContainer = document.getElementById('recent-sheets');

            if (!response.answer_sheets || response.answer_sheets.length === 0) {
                recentSheetsContainer.innerHTML = `
                    <div class="text-gray-500 text-center py-8">
                        <i class="fas fa-inbox text-4xl mb-4"></i>
                        <p>No answer sheets uploaded yet. Upload your first answer sheet to get started!</p>
                    </div>
                `;
                return;
            }

            const sheetsHtml = response.answer_sheets.map(sheet => `
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-150">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <i class="fas fa-file-alt text-2xl text-gray-400"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">${sheet.student_name}</h4>
                            <p class="text-xs text-gray-500">${sheet.marking_guide_title || 'Unknown Guide'}</p>
                            <p class="text-xs text-gray-400">${formatDate(sheet.created_at)}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="text-right">
                            <div class="text-sm font-medium ${this.getScoreColor(sheet.percentage_score)}">
                                ${sheet.percentage_score ? sheet.percentage_score.toFixed(1) + '%' : 'Processing...'}
                            </div>
                            <div class="text-xs ${this.getStatusColor(sheet.processing_status)}">
                                ${this.getStatusText(sheet.processing_status)}
                            </div>
                        </div>
                        <button onclick="Dashboard.viewAnswerSheet(${sheet.id})" 
                                class="text-primary hover:text-secondary">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            recentSheetsContainer.innerHTML = sheetsHtml;

        } catch (error) {
            console.error('Error loading recent sheets:', error);
            document.getElementById('recent-sheets').innerHTML = `
                <div class="text-red-500 text-center py-8">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                    <p>Error loading recent answer sheets</p>
                </div>
            `;
        }
    },

    // Get status color class
    getStatusColor(status) {
        const colors = {
            'uploaded': 'text-blue-600',
            'processing': 'text-yellow-600',
            'processed': 'text-green-600',
            'marked': 'text-green-700',
            'error': 'text-red-600'
        };
        return colors[status] || 'text-gray-600';
    },

    // Get status text
    getStatusText(status) {
        const texts = {
            'uploaded': 'Uploaded',
            'processing': 'Processing',
            'processed': 'Processed',
            'marked': 'Marked',
            'error': 'Error'
        };
        return texts[status] || status;
    },

    // Get score color class
    getScoreColor(score) {
        if (!score) return 'text-gray-500';
        if (score >= 80) return 'text-green-600';
        if (score >= 60) return 'text-yellow-600';
        return 'text-red-600';
    },

    // Show upload modal
    showUploadModal() {
        const content = `
            <div class="space-y-6">
                <!-- Scan Instructions -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Scanning Instructions</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Use a scanner or smartphone camera to scan the answer sheet</li>
                                    <li>Ensure good lighting and clear image quality</li>
                                    <li>Keep the paper flat and avoid shadows</li>
                                    <li>Scan in color or high-quality grayscale</li>
                                    <li>Save as JPG, PNG, or PDF format</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="upload-form" class="space-y-4">
                    <!-- Scan Upload Section -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                        <div class="space-y-4">
                            <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-camera text-2xl text-gray-400"></i>
                            </div>
                            <div>
                                <label for="answer-sheet-file" class="cursor-pointer">
                                    <span class="text-lg font-medium text-gray-900">Scan & Upload Answer Sheet</span>
                                    <p class="text-sm text-gray-500 mt-1">Click to select scanned image or drag and drop</p>
                                </label>
                                <input type="file" id="answer-sheet-file" name="file" accept="image/*,.pdf" required
                                       class="hidden" capture="environment">
                            </div>
                            <div class="flex justify-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-mobile-alt mr-1"></i>Phone Camera</span>
                                <span><i class="fas fa-scanner mr-1"></i>Scanner</span>
                                <span><i class="fas fa-file-image mr-1"></i>Image File</span>
                            </div>
                        </div>
                    </div>

                    <div id="file-preview" class="file-preview"></div>

                    <!-- Student Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="student-name" class="block text-sm font-medium text-gray-700">Student Name *</label>
                            <input type="text" id="student-name" name="student_name" required
                                   placeholder="Enter student's full name"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label for="student-id" class="block text-sm font-medium text-gray-700">Student ID</label>
                            <input type="text" id="student-id" name="student_id"
                                   placeholder="e.g., STU001, 2024001"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="class-name" class="block text-sm font-medium text-gray-700">Class/Section</label>
                            <input type="text" id="class-name" name="class_name"
                                   placeholder="e.g., Grade 10A, Math 101"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label for="exam-date" class="block text-sm font-medium text-gray-700">Exam Date</label>
                            <input type="date" id="exam-date" name="exam_date"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                    </div>

                    <!-- Marking Guide Selection -->
                    <div>
                        <label for="marking-guide-select" class="block text-sm font-medium text-gray-700">Select Marking Guide *</label>
                        <select id="marking-guide-select" name="marking_guide_id" required
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">Choose the marking guide for this test...</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Don't see your marking guide? <a href="#" onclick="App.showMarkingGuides()" class="text-primary hover:underline">Create one first</a></p>
                    </div>

                    <div id="upload-error" class="hidden text-red-600 text-sm"></div>
                </form>
            </div>
        `;

        const actions = [
            {
                text: 'Upload & Process',
                class: 'bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md',
                onclick: 'Dashboard.handleUpload()'
            }
        ];

        createModal('Upload Answer Sheet', content, actions);

        // Load marking guides
        this.loadMarkingGuidesForUpload();

        // Setup file preview with enhanced features
        const fileInput = document.getElementById('answer-sheet-file');
        const previewDiv = document.getElementById('file-preview');
        handleFileUpload(fileInput, previewDiv, ['image/*', 'application/pdf']);

        // Add camera capture support if available
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            addCameraCapture(fileInput);
        }
    },

    // Load marking guides for upload form
    async loadMarkingGuidesForUpload() {
        try {
            const response = await api.getMarkingGuides({ is_active: true, per_page: 100 });
            const select = document.getElementById('marking-guide-select');

            if (response.marking_guides && response.marking_guides.length > 0) {
                const options = response.marking_guides.map(guide => 
                    `<option value="${guide.id}">${guide.title} (${guide.subject})</option>`
                ).join('');
                select.innerHTML = '<option value="">Select a marking guide...</option>' + options;
            } else {
                select.innerHTML = '<option value="">No marking guides available</option>';
            }
        } catch (error) {
            console.error('Error loading marking guides:', error);
            const select = document.getElementById('marking-guide-select');
            select.innerHTML = '<option value="">Error loading marking guides</option>';
        }
    },

    // Handle file upload
    async handleUpload() {
        const form = document.getElementById('upload-form');
        const errorDiv = document.getElementById('upload-error');
        const formData = new FormData(form);

        // Validation
        if (!formData.get('marking_guide_id')) {
            this.showUploadError(errorDiv, 'Please select a marking guide');
            return;
        }

        if (!formData.get('student_name')) {
            this.showUploadError(errorDiv, 'Please enter student name');
            return;
        }

        if (!formData.get('file')) {
            this.showUploadError(errorDiv, 'Please select a file');
            return;
        }

        try {
            showLoading();
            const response = await api.uploadAnswerSheet(formData);
            hideLoading();

            closeModal();
            showToast('Answer sheet uploaded successfully! Processing started.', 'success');

            // Refresh dashboard data
            this.loadDashboardData();

        } catch (error) {
            hideLoading();
            this.showUploadError(errorDiv, error.message);
        }
    },

    // Show upload error
    showUploadError(errorDiv, message) {
        errorDiv.innerHTML = message;
        errorDiv.classList.remove('hidden');
    },

    // View answer sheet details
    viewAnswerSheet(sheetId) {
        App.showAnswerSheetDetails(sheetId);
    }
};

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Dashboard.init();
});
