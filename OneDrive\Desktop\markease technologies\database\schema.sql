-- MarkEase Database Schema
-- MySQL Database Schema for the MarkEase Application

-- Create database
CREATE DATABASE IF NOT EXISTS markease_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS markease_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS markease CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE markease_dev;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(80) NOT NULL UNIQUE,
    email VARCHAR(120) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    role ENUM('teacher', 'admin', 'student') NOT NULL DEFAULT 'teacher',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- Marking guides table
CREATE TABLE marking_guides (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    subject VARCHAR(100) NOT NULL,
    grade_level VARCHAR(20),
    total_marks INT NOT NULL DEFAULT 0,
    time_limit INT, -- in minutes
    instructions TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    teacher_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_subject (subject),
    INDEX idx_is_active (is_active)
);

-- Questions table
CREATE TABLE questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question_number INT NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('multiple_choice', 'short_answer', 'essay', 'true_false') NOT NULL,
    marks INT NOT NULL DEFAULT 1,
    correct_answer TEXT,
    answer_options TEXT, -- JSON string for MCQ options
    marking_criteria TEXT,
    keywords TEXT, -- JSON string of keywords
    case_sensitive BOOLEAN NOT NULL DEFAULT FALSE,
    partial_marks BOOLEAN NOT NULL DEFAULT TRUE,
    marking_guide_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (marking_guide_id) REFERENCES marking_guides(id) ON DELETE CASCADE,
    INDEX idx_marking_guide_id (marking_guide_id),
    INDEX idx_question_number (question_number),
    INDEX idx_question_type (question_type)
);

-- Answer sheets table
CREATE TABLE answer_sheets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_name VARCHAR(100) NOT NULL,
    student_id VARCHAR(50),
    class_name VARCHAR(50),
    exam_date DATE,
    file_path VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size INT,
    processing_status ENUM('uploaded', 'processing', 'processed', 'marked', 'error') NOT NULL DEFAULT 'uploaded',
    ocr_text TEXT,
    total_score FLOAT NOT NULL DEFAULT 0.0,
    percentage_score FLOAT NOT NULL DEFAULT 0.0,
    processing_notes TEXT,
    is_reviewed BOOLEAN NOT NULL DEFAULT FALSE,
    teacher_id INT NOT NULL,
    marking_guide_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    processed_at DATETIME,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (marking_guide_id) REFERENCES marking_guides(id) ON DELETE CASCADE,
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_marking_guide_id (marking_guide_id),
    INDEX idx_student_name (student_name),
    INDEX idx_processing_status (processing_status),
    INDEX idx_exam_date (exam_date)
);

-- Answers table
CREATE TABLE answers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_answer TEXT,
    marks_obtained FLOAT NOT NULL DEFAULT 0.0,
    is_correct BOOLEAN NOT NULL DEFAULT FALSE,
    confidence_score FLOAT,
    manual_review BOOLEAN NOT NULL DEFAULT FALSE,
    reviewer_notes TEXT,
    auto_marked BOOLEAN NOT NULL DEFAULT TRUE,
    question_id INT NOT NULL,
    answer_sheet_id INT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    FOREIGN KEY (answer_sheet_id) REFERENCES answer_sheets(id) ON DELETE CASCADE,
    INDEX idx_question_id (question_id),
    INDEX idx_answer_sheet_id (answer_sheet_id),
    INDEX idx_is_correct (is_correct),
    INDEX idx_manual_review (manual_review)
);

-- Reports table
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    report_type ENUM('individual', 'class_summary', 'subject_analysis') NOT NULL,
    report_data TEXT, -- JSON string
    file_path VARCHAR(255),
    summary TEXT,
    teacher_id INT NOT NULL,
    answer_sheet_id INT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (answer_sheet_id) REFERENCES answer_sheets(id) ON DELETE SET NULL,
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_report_type (report_type),
    INDEX idx_answer_sheet_id (answer_sheet_id)
);
