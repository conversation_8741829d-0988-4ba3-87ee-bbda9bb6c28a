// Utility functions for the MarkEase application

// Show loading overlay
function showLoading() {
    document.getElementById('loading-overlay').classList.remove('hidden');
}

// Hide loading overlay
function hideLoading() {
    document.getElementById('loading-overlay').classList.add('hidden');
}

// Show toast notification
function showToast(message, type = 'info', duration = 5000) {
    const toastContainer = document.getElementById('toast-container');
    const toast = document.createElement('div');
    
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';
    
    const icon = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    }[type] || 'fas fa-info-circle';
    
    toast.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 transform transition-all duration-300 translate-x-full`;
    toast.innerHTML = `
        <i class="${icon}"></i>
        <span class="flex-1">${message}</span>
        <button onclick="this.parentElement.remove()" class="text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    toastContainer.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, duration);
}

// Format date
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Format file size
function formatFileSize(bytes) {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// Validate email
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Validate password strength
function validatePassword(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    
    const errors = [];
    
    if (password.length < minLength) {
        errors.push(`Password must be at least ${minLength} characters long`);
    }
    if (!hasUpperCase) {
        errors.push('Password must contain at least one uppercase letter');
    }
    if (!hasLowerCase) {
        errors.push('Password must contain at least one lowercase letter');
    }
    if (!hasNumbers) {
        errors.push('Password must contain at least one number');
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

// Create modal
function createModal(title, content, actions = []) {
    let modalContainer = document.getElementById('modal-container');

    // Create modal container if it doesn't exist
    if (!modalContainer) {
        modalContainer = document.createElement('div');
        modalContainer.id = 'modal-container';
        document.body.appendChild(modalContainer);
    }

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.id = 'current-modal';
    
    const actionsHtml = actions.map(action => 
        `<button onclick="${action.onclick}" class="${action.class || 'bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md'}">${action.text}</button>`
    ).join('');
    
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="mb-6">
                ${content}
            </div>
            <div class="flex justify-end space-x-3">
                ${actionsHtml}
                <button onclick="closeModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">Cancel</button>
            </div>
        </div>
    `;
    
    modalContainer.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    return modal;
}

// Close modal
function closeModal() {
    const modal = document.getElementById('current-modal');
    if (modal) {
        modal.remove();
    }
}

// Create confirmation dialog
function confirmDialog(message, onConfirm, onCancel = null) {
    const actions = [
        {
            text: 'Confirm',
            class: 'bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md',
            onclick: `closeModal(); (${onConfirm.toString()})()`
        }
    ];
    
    if (onCancel) {
        actions.push({
            text: 'Cancel',
            class: 'bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md',
            onclick: `closeModal(); (${onCancel.toString()})()`
        });
    }
    
    createModal('Confirm Action', `<p class="text-gray-600">${message}</p>`, actions);
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Get query parameters
function getQueryParams() {
    const params = new URLSearchParams(window.location.search);
    const result = {};
    for (const [key, value] of params) {
        result[key] = value;
    }
    return result;
}

// Update URL without reload
function updateURL(params) {
    const url = new URL(window.location);
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            url.searchParams.set(key, params[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    window.history.pushState({}, '', url);
}

// Create pagination
function createPagination(pagination, onPageChange) {
    if (!pagination || pagination.pages <= 1) {
        return '';
    }
    
    let paginationHtml = '<div class="flex items-center justify-between mt-6">';
    
    // Previous button
    if (pagination.has_prev) {
        paginationHtml += `<button onclick="${onPageChange}(${pagination.page - 1})" class="bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 px-4 py-2 rounded-md">Previous</button>`;
    } else {
        paginationHtml += '<div></div>';
    }
    
    // Page info
    paginationHtml += `<span class="text-sm text-gray-700">Page ${pagination.page} of ${pagination.pages} (${pagination.total} total)</span>`;
    
    // Next button
    if (pagination.has_next) {
        paginationHtml += `<button onclick="${onPageChange}(${pagination.page + 1})" class="bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 px-4 py-2 rounded-md">Next</button>`;
    } else {
        paginationHtml += '<div></div>';
    }
    
    paginationHtml += '</div>';
    
    return paginationHtml;
}

// Handle file upload with preview and camera support
function handleFileUpload(inputElement, previewElement, allowedTypes = ['image/*', 'application/pdf']) {
    // Add drag and drop support
    const parentElement = inputElement.closest('.border-dashed') || inputElement.parentElement;

    // Drag and drop events
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        parentElement.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        parentElement.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        parentElement.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        parentElement.classList.add('border-primary', 'bg-blue-50');
    }

    function unhighlight(e) {
        parentElement.classList.remove('border-primary', 'bg-blue-50');
    }

    // Handle dropped files
    parentElement.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            inputElement.files = files;
            handleFileChange(files[0]);
        }
    }

    // Handle file input change
    inputElement.addEventListener('change', function(e) {
        const file = e.target.files[0];
        handleFileChange(file);
    });

    function handleFileChange(file) {
        if (!file) {
            previewElement.innerHTML = '';
            return;
        }

        // Validate file type
        const isValidType = allowedTypes.some(type => {
            if (type.endsWith('/*')) {
                return file.type.startsWith(type.slice(0, -1));
            }
            return file.type === type;
        });

        if (!isValidType) {
            showToast('Invalid file type. Please select a valid image or PDF file.', 'error');
            inputElement.value = '';
            previewElement.innerHTML = '';
            return;
        }

        // Check file size (16MB limit)
        const maxSize = 16 * 1024 * 1024; // 16MB
        if (file.size > maxSize) {
            showToast('File too large. Please select a file smaller than 16MB.', 'error');
            inputElement.value = '';
            previewElement.innerHTML = '';
            return;
        }

        // Show file info with enhanced preview
        previewElement.innerHTML = `
            <div class="bg-white border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-${file.type.startsWith('image/') ? 'image' : 'file-pdf'} text-2xl text-green-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">${file.name}</p>
                            <p class="text-xs text-gray-500">${formatFileSize(file.size)} • ${file.type}</p>
                        </div>
                    </div>
                    <button onclick="clearFileUpload('${inputElement.id}', '${previewElement.id}')"
                            class="text-red-500 hover:text-red-700 p-1">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center space-x-4 text-sm text-gray-600">
                        <span class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-1"></i>
                            File uploaded successfully
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock text-blue-500 mr-1"></i>
                            Ready for processing
                        </span>
                    </div>

                    ${file.type.startsWith('image/') ? '<div id="image-preview-container"></div>' : ''}
                </div>
            </div>
        `;

        // If it's an image, show preview
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const container = document.getElementById('image-preview-container');
                if (container) {
                    container.innerHTML = `
                        <div class="mt-3">
                            <p class="text-xs text-gray-500 mb-2">Preview:</p>
                            <img src="${e.target.result}" alt="Scanned document preview"
                                 class="max-w-full h-64 object-contain rounded-md border border-gray-200 bg-gray-50">
                        </div>
                    `;
                }
            };
            reader.readAsDataURL(file);
        }
    }
}

// Clear file upload
function clearFileUpload(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);

    if (input) input.value = '';
    if (preview) preview.innerHTML = '';
}

// Add camera capture support
function addCameraCapture(inputElement) {
    // Check if device supports camera
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        const cameraButton = document.createElement('button');
        cameraButton.type = 'button';
        cameraButton.className = 'mt-2 bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm';
        cameraButton.innerHTML = '<i class="fas fa-camera mr-1"></i>Take Photo';

        cameraButton.addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment', // Use back camera if available
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    }
                });

                showCameraModal(stream, (capturedFile) => {
                    // Create a new FileList with the captured image
                    const dt = new DataTransfer();
                    dt.items.add(capturedFile);
                    inputElement.files = dt.files;

                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    inputElement.dispatchEvent(event);
                });

            } catch (error) {
                console.error('Camera access error:', error);
                showToast('Camera access denied or not available', 'error');
            }
        });

        inputElement.parentElement.appendChild(cameraButton);
    }
}

// Show camera modal for capturing
function showCameraModal(stream, onCapture) {
    const modal = createModal('Capture Document', `
        <div class="text-center">
            <video id="camera-video" autoplay playsinline class="w-full max-w-md mx-auto rounded-lg border"></video>
            <div class="mt-4 space-x-3">
                <button onclick="capturePhoto()" class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md">
                    <i class="fas fa-camera mr-2"></i>Capture
                </button>
                <button onclick="closeCameraModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                    Cancel
                </button>
            </div>
            <canvas id="capture-canvas" class="hidden"></canvas>
        </div>
    `, []);

    const video = document.getElementById('camera-video');
    video.srcObject = stream;

    window.capturePhoto = () => {
        const canvas = document.getElementById('capture-canvas');
        const context = canvas.getContext('2d');

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0);

        canvas.toBlob((blob) => {
            const file = new File([blob], `captured-${Date.now()}.jpg`, { type: 'image/jpeg' });
            onCapture(file);
            closeCameraModal();
        }, 'image/jpeg', 0.8);
    };

    window.closeCameraModal = () => {
        stream.getTracks().forEach(track => track.stop());
        closeModal();
        delete window.capturePhoto;
        delete window.closeCameraModal;
    };
}
