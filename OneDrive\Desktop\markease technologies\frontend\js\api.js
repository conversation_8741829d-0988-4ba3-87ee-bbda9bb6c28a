// API client for MarkEase application

class APIClient {
    constructor() {
        this.baseURL = 'http://localhost:5000/api';
        this.token = localStorage.getItem('access_token');
    }

    // Set authentication token
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('access_token', token);
        } else {
            localStorage.removeItem('access_token');
        }
    }

    // Get authentication headers
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (includeAuth && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        return headers;
    }

    // Make API request
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(options.auth !== false),
            ...options
        };

        try {
            console.log('Making API request:', { url, config });
            const response = await fetch(url, config);

            let data;
            try {
                data = await response.json();
            } catch (e) {
                data = { error: 'Invalid JSON response' };
            }

            console.log('API response:', { status: response.status, data });

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // Authentication endpoints
    async login(username, password) {
        return this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password }),
            auth: false
        });
    }

    async register(userData) {
        return this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData),
            auth: false
        });
    }

    async getProfile() {
        return this.request('/auth/profile');
    }

    async updateProfile(userData) {
        return this.request('/auth/profile', {
            method: 'PUT',
            body: JSON.stringify(userData)
        });
    }

    async changePassword(currentPassword, newPassword) {
        return this.request('/auth/change-password', {
            method: 'POST',
            body: JSON.stringify({
                current_password: currentPassword,
                new_password: newPassword
            })
        });
    }

    // Marking guides endpoints
    async getMarkingGuides(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/marking-guides?${queryString}`);
    }

    async createMarkingGuide(guideData) {
        return this.request('/marking-guides', {
            method: 'POST',
            body: JSON.stringify(guideData)
        });
    }

    async getMarkingGuide(guideId) {
        return this.request(`/marking-guides/${guideId}`);
    }

    async updateMarkingGuide(guideId, guideData) {
        return this.request(`/marking-guides/${guideId}`, {
            method: 'PUT',
            body: JSON.stringify(guideData)
        });
    }

    async deleteMarkingGuide(guideId) {
        return this.request(`/marking-guides/${guideId}`, {
            method: 'DELETE'
        });
    }

    async addQuestion(guideId, questionData) {
        return this.request(`/marking-guides/${guideId}/questions`, {
            method: 'POST',
            body: JSON.stringify(questionData)
        });
    }

    async scanAnswerKey(formData) {
        return this.request('/marking-guides/scan-answer-key', {
            method: 'POST',
            body: formData,
            headers: this.token ? { 'Authorization': `Bearer ${this.token}` } : {}
        });
    }

    // Answer sheets endpoints
    async getAnswerSheets(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/answer-sheets?${queryString}`);
    }

    async uploadAnswerSheet(formData) {
        return this.request('/answer-sheets/upload', {
            method: 'POST',
            body: formData,
            headers: this.token ? { 'Authorization': `Bearer ${this.token}` } : {}
        });
    }

    async getAnswerSheet(sheetId) {
        return this.request(`/answer-sheets/${sheetId}`);
    }

    async updateAnswer(sheetId, answerId, answerData) {
        return this.request(`/answer-sheets/${sheetId}/answers/${answerId}`, {
            method: 'PUT',
            body: JSON.stringify(answerData)
        });
    }

    // Reports endpoints
    async getReports(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return this.request(`/reports?${queryString}`);
    }

    async generateIndividualReport(answerSheetId) {
        return this.request(`/reports/individual/${answerSheetId}`, {
            method: 'POST'
        });
    }

    async generateClassSummary(markingGuideId) {
        return this.request(`/reports/class-summary/${markingGuideId}`, {
            method: 'POST'
        });
    }

    async getReport(reportId) {
        return this.request(`/reports/${reportId}`);
    }

    // Health check
    async healthCheck() {
        return this.request('/health', { auth: false });
    }
}

// Create global API client instance
const api = new APIClient();

// API helper functions
const ApiHelpers = {
    // Handle API errors
    handleError(error, defaultMessage = 'An error occurred') {
        console.error('API Error:', error);
        const message = error.message || defaultMessage;
        showToast(message, 'error');
        return null;
    },

    // Handle successful responses
    handleSuccess(data, message = null) {
        if (message) {
            showToast(message, 'success');
        }
        return data;
    },

    // Wrapper for API calls with error handling
    async safeCall(apiCall, successMessage = null, errorMessage = null) {
        try {
            showLoading();
            const result = await apiCall();
            hideLoading();
            return this.handleSuccess(result, successMessage);
        } catch (error) {
            hideLoading();
            return this.handleError(error, errorMessage);
        }
    },

    // Check if user is authenticated
    isAuthenticated() {
        return !!api.token;
    },

    // Logout user
    logout() {
        api.setToken(null);
        localStorage.removeItem('user_data');
        window.location.reload();
    },

    // Get stored user data
    getUserData() {
        const userData = localStorage.getItem('user_data');
        return userData ? JSON.parse(userData) : null;
    },

    // Store user data
    setUserData(userData) {
        localStorage.setItem('user_data', JSON.stringify(userData));
    },

    // Format API date
    formatApiDate(dateString) {
        if (!dateString) return null;
        return new Date(dateString).toISOString().split('T')[0];
    },

    // Create form data for file uploads
    createFormData(data, fileField = 'file') {
        const formData = new FormData();
        
        Object.keys(data).forEach(key => {
            if (data[key] !== null && data[key] !== undefined) {
                if (key === fileField && data[key] instanceof File) {
                    formData.append(key, data[key]);
                } else {
                    formData.append(key, data[key].toString());
                }
            }
        });
        
        return formData;
    },

    // Validate response data
    validateResponse(response, requiredFields = []) {
        if (!response) {
            throw new Error('No response received');
        }

        for (const field of requiredFields) {
            if (!(field in response)) {
                throw new Error(`Missing required field: ${field}`);
            }
        }

        return true;
    },

    // Retry API call
    async retryCall(apiCall, maxRetries = 3, delay = 1000) {
        let lastError;
        
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await apiCall();
            } catch (error) {
                lastError = error;
                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
                }
            }
        }
        
        throw lastError;
    },

    // Batch API calls
    async batchCalls(apiCalls, concurrency = 3) {
        const results = [];
        const errors = [];
        
        for (let i = 0; i < apiCalls.length; i += concurrency) {
            const batch = apiCalls.slice(i, i + concurrency);
            const batchPromises = batch.map(async (call, index) => {
                try {
                    const result = await call();
                    return { index: i + index, result, error: null };
                } catch (error) {
                    return { index: i + index, result: null, error };
                }
            });
            
            const batchResults = await Promise.all(batchPromises);
            
            batchResults.forEach(({ index, result, error }) => {
                if (error) {
                    errors.push({ index, error });
                } else {
                    results[index] = result;
                }
            });
        }
        
        return { results, errors };
    }
};

// Export for use in other modules
window.api = api;
window.ApiHelpers = ApiHelpers;
