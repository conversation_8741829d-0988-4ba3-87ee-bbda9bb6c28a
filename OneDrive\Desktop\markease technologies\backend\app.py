#!/usr/bin/env python3
"""
MarkEase Application Entry Point
"""
import os
from app import create_app, db
from app.models.user import User
from app.models.answer_sheet import AnswerSheet
from app.models.marking_guide import MarkingGuide
from app.models.question import Question
from app.models.answer import Answer
from app.models.report import Report

app = create_app(os.getenv('FLASK_ENV'))

@app.shell_context_processor
def make_shell_context():
    """Make database models available in flask shell."""
    return {
        'db': db,
        'User': User,
        'AnswerSheet': AnswerSheet,
        'MarkingGuide': MarkingGuide,
        'Question': Question,
        'Answer': Answer,
        'Report': Report
    }

@app.cli.command()
def init_db():
    """Initialize the database."""
    db.create_all()
    print("Database initialized!")

@app.cli.command()
def seed_db():
    """Seed the database with sample data."""
    from app.utils.seed_data import seed_database
    seed_database()
    print("Database seeded with sample data!")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
