// Main application module for MarkEase

const App = {
    currentPage: 'dashboard',
    
    // Initialize application
    init() {
        this.setupEventListeners();
        this.checkInitialRoute();
    },

    // Setup global event listeners
    setupEventListeners() {
        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            this.handleRouteChange();
        });

        // Handle navigation clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-route]')) {
                e.preventDefault();
                const route = e.target.getAttribute('data-route');
                this.navigateTo(route);
            }
        });
    },

    // Check initial route
    checkInitialRoute() {
        const params = getQueryParams();
        if (params.page) {
            this.navigateTo(params.page);
        }
    },

    // Navigate to a page
    navigateTo(page, params = {}) {
        this.currentPage = page;
        updateURL({ page, ...params });
        this.showPage(page, params);
    },

    // Handle route changes
    handleRouteChange() {
        const params = getQueryParams();
        const page = params.page || 'dashboard';
        this.showPage(page, params);
    },

    // Show specific page
    showPage(page, params = {}) {
        // Hide all sections
        document.getElementById('welcome-section')?.classList.add('hidden');
        document.getElementById('dashboard-section')?.classList.add('hidden');
        document.getElementById('content-area')?.classList.add('hidden');

        // Update navigation active state
        this.updateNavigationActive(page);

        // Show appropriate content
        switch (page) {
            case 'dashboard':
                this.showDashboard();
                break;
            case 'answer-sheets':
                this.showAnswerSheets(params);
                break;
            case 'answer-sheet':
                this.showAnswerSheetDetails(params.id);
                break;
            case 'marking-guides':
                this.showMarkingGuides(params);
                break;
            case 'marking-guide':
                this.showMarkingGuideDetails(params.id);
                break;
            case 'reports':
                this.showReports(params);
                break;
            case 'report':
                this.showReportDetails(params.id);
                break;
            default:
                this.showDashboard();
        }
    },

    // Update navigation active state
    updateNavigationActive(activePage) {
        const navLinks = document.querySelectorAll('#main-nav a');
        navLinks.forEach(link => {
            link.classList.remove('text-gray-900');
            link.classList.add('text-gray-500');
        });

        // Set active link
        const activeLink = document.querySelector(`#main-nav a[onclick*="${activePage}"]`);
        if (activeLink) {
            activeLink.classList.remove('text-gray-500');
            activeLink.classList.add('text-gray-900');
        }
    },

    // Show dashboard
    showDashboard() {
        if (!ApiHelpers.isAuthenticated()) {
            document.getElementById('welcome-section').classList.remove('hidden');
            return;
        }

        document.getElementById('dashboard-section').classList.remove('hidden');
        Dashboard.loadDashboardData();
    },

    // Show answer sheets list
    async showAnswerSheets(params = {}) {
        if (!ApiHelpers.isAuthenticated()) return;

        const contentArea = document.getElementById('content-area');
        contentArea.classList.remove('hidden');

        const page = parseInt(params.page) || 1;
        const status = params.status || '';
        const search = params.search || '';

        try {
            showLoading();
            const response = await api.getAnswerSheets({
                page,
                per_page: 10,
                status,
                student_name: search
            });
            hideLoading();

            const content = `
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-900">Answer Sheets</h2>
                            <p class="text-gray-600 mt-2">Manage and review uploaded answer sheets</p>
                        </div>
                        <button onclick="Dashboard.showUploadModal()" 
                                class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md font-medium">
                            <i class="fas fa-upload mr-2"></i>Upload Answer Sheet
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white p-4 rounded-lg shadow mb-6">
                    <div class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" id="search-input" placeholder="Search by student name..." 
                                   value="${search}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <select id="status-filter" 
                                    class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                                <option value="">All Status</option>
                                <option value="uploaded" ${status === 'uploaded' ? 'selected' : ''}>Uploaded</option>
                                <option value="processing" ${status === 'processing' ? 'selected' : ''}>Processing</option>
                                <option value="processed" ${status === 'processed' ? 'selected' : ''}>Processed</option>
                                <option value="marked" ${status === 'marked' ? 'selected' : ''}>Marked</option>
                                <option value="error" ${status === 'error' ? 'selected' : ''}>Error</option>
                            </select>
                        </div>
                        <button onclick="App.applyAnswerSheetFilters()" 
                                class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </div>

                <!-- Answer Sheets List -->
                <div class="bg-white shadow rounded-lg">
                    ${this.renderAnswerSheetsList(response.answer_sheets)}
                    ${createPagination(response.pagination, 'App.goToAnswerSheetsPage')}
                </div>
            `;

            contentArea.innerHTML = content;

        } catch (error) {
            hideLoading();
            contentArea.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Answer Sheets</h3>
                    <p class="text-gray-600">${error.message}</p>
                </div>
            `;
        }
    },

    // Render answer sheets list
    renderAnswerSheetsList(sheets) {
        if (!sheets || sheets.length === 0) {
            return `
                <div class="text-center py-12">
                    <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Answer Sheets Found</h3>
                    <p class="text-gray-600">Upload your first answer sheet to get started!</p>
                </div>
            `;
        }

        const tableRows = sheets.map(sheet => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-file-alt text-gray-500"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${sheet.student_name}</div>
                            <div class="text-sm text-gray-500">${sheet.student_id || 'No ID'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${sheet.marking_guide_title || 'Unknown'}</div>
                    <div class="text-sm text-gray-500">${sheet.class_name || 'No class'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${Dashboard.getStatusColor(sheet.processing_status)} bg-opacity-10">
                        ${Dashboard.getStatusText(sheet.processing_status)}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="font-medium ${Dashboard.getScoreColor(sheet.percentage_score)}">
                        ${sheet.percentage_score ? sheet.percentage_score.toFixed(1) + '%' : 'N/A'}
                    </div>
                    <div class="text-xs text-gray-500">${sheet.grade || 'N/A'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${formatDate(sheet.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button onclick="App.showAnswerSheetDetails(${sheet.id})" 
                            class="text-primary hover:text-secondary mr-3">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${sheet.processing_status === 'marked' ? 
                        `<button onclick="App.generateIndividualReport(${sheet.id})" 
                                 class="text-green-600 hover:text-green-800">
                            <i class="fas fa-chart-bar"></i>
                         </button>` : ''
                    }
                </td>
            </tr>
        `).join('');

        return `
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        `;
    },

    // Apply answer sheet filters
    applyAnswerSheetFilters() {
        const search = document.getElementById('search-input').value;
        const status = document.getElementById('status-filter').value;
        
        this.navigateTo('answer-sheets', { search, status, page: 1 });
    },

    // Go to specific page
    goToAnswerSheetsPage(page) {
        const params = getQueryParams();
        this.navigateTo('answer-sheets', { ...params, page });
    },

    // Show answer sheet details
    async showAnswerSheetDetails(sheetId) {
        if (!ApiHelpers.isAuthenticated()) return;

        const contentArea = document.getElementById('content-area');
        contentArea.classList.remove('hidden');

        try {
            showLoading();
            const response = await api.getAnswerSheet(sheetId);
            hideLoading();

            const sheet = response.answer_sheet;
            
            // Render answer sheet details
            // This would be a complex component showing the sheet details,
            // answers, and allowing manual review
            contentArea.innerHTML = `
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-900">${sheet.student_name}'s Answer Sheet</h2>
                            <p class="text-gray-600 mt-2">${sheet.marking_guide_title}</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="App.showAnswerSheets()" 
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                                <i class="fas fa-arrow-left mr-2"></i>Back to List
                            </button>
                            ${sheet.processing_status === 'marked' ? 
                                `<button onclick="App.generateIndividualReport(${sheet.id})" 
                                         class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md">
                                    <i class="fas fa-chart-bar mr-2"></i>Generate Report
                                 </button>` : ''
                            }
                        </div>
                    </div>
                </div>

                <!-- Sheet Info -->
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Student Information</h3>
                            <div class="mt-2">
                                <p class="text-lg font-medium text-gray-900">${sheet.student_name}</p>
                                <p class="text-sm text-gray-600">ID: ${sheet.student_id || 'N/A'}</p>
                                <p class="text-sm text-gray-600">Class: ${sheet.class_name || 'N/A'}</p>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Test Information</h3>
                            <div class="mt-2">
                                <p class="text-lg font-medium text-gray-900">${sheet.marking_guide_title}</p>
                                <p class="text-sm text-gray-600">Exam Date: ${sheet.exam_date || 'N/A'}</p>
                                <p class="text-sm text-gray-600">Uploaded: ${formatDate(sheet.created_at)}</p>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Results</h3>
                            <div class="mt-2">
                                <p class="text-2xl font-bold ${Dashboard.getScoreColor(sheet.percentage_score)}">
                                    ${sheet.percentage_score ? sheet.percentage_score.toFixed(1) + '%' : 'Processing...'}
                                </p>
                                <p class="text-sm text-gray-600">Grade: ${sheet.grade || 'N/A'}</p>
                                <p class="text-sm ${Dashboard.getStatusColor(sheet.processing_status)}">
                                    ${Dashboard.getStatusText(sheet.processing_status)}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Answers -->
                ${sheet.answers ? this.renderAnswersList(sheet.answers, sheetId) : '<div class="text-center py-8">No answers available</div>'}
            `;

        } catch (error) {
            hideLoading();
            contentArea.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Answer Sheet</h3>
                    <p class="text-gray-600">${error.message}</p>
                </div>
            `;
        }
    },

    // Render answers list
    renderAnswersList(answers, sheetId) {
        if (!answers || answers.length === 0) {
            return '<div class="text-center py-8">No answers found</div>';
        }

        const answersHtml = answers.map(answer => `
            <div class="bg-white shadow rounded-lg p-6 mb-4">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900">Question ${answer.question_number}</h4>
                        <p class="text-gray-600 mt-1">${answer.question_text}</p>
                        <span class="inline-block mt-2 px-2 py-1 text-xs font-medium rounded-full ${
                            answer.question_type === 'multiple_choice' ? 'bg-blue-100 text-blue-800' :
                            answer.question_type === 'short_answer' ? 'bg-green-100 text-green-800' :
                            answer.question_type === 'essay' ? 'bg-purple-100 text-purple-800' :
                            'bg-gray-100 text-gray-800'
                        }">
                            ${answer.question_type.replace('_', ' ').toUpperCase()}
                        </span>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold ${Dashboard.getScoreColor(answer.percentage_score)}">
                            ${answer.marks_obtained}/${answer.total_marks}
                        </div>
                        <div class="text-sm text-gray-500">${answer.percentage_score.toFixed(1)}%</div>
                    </div>
                </div>
                
                <div class="border-t pt-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-2">Student Answer:</h5>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-sm text-gray-900">${answer.student_answer || 'No answer provided'}</p>
                            </div>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-2">Feedback:</h5>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-sm text-gray-900">${answer.feedback || 'No feedback available'}</p>
                            </div>
                        </div>
                    </div>
                    
                    ${answer.manual_review ? 
                        '<div class="mt-3 text-sm text-blue-600"><i class="fas fa-user-edit mr-1"></i>Manually reviewed</div>' :
                        `<div class="mt-3">
                            <button onclick="App.editAnswer(${sheetId}, ${answer.id})" 
                                    class="text-sm text-primary hover:text-secondary">
                                <i class="fas fa-edit mr-1"></i>Edit Marks
                            </button>
                         </div>`
                    }
                </div>
            </div>
        `).join('');

        return `
            <div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">Answers</h3>
                ${answersHtml}
            </div>
        `;
    },

    // Show marking guides
    async showMarkingGuides(params = {}) {
        console.log('showMarkingGuides called', params);
        if (!ApiHelpers.isAuthenticated()) {
            console.log('User not authenticated');
            return;
        }

        const contentArea = document.getElementById('content-area');
        contentArea.classList.remove('hidden');

        try {
            showLoading();
            console.log('Calling api.getMarkingGuides...');
            const response = await api.getMarkingGuides({ page: 1, per_page: 10 });
            console.log('API response:', response);
            hideLoading();

            const content = `
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-900">Marking Guides</h2>
                            <p class="text-gray-600 mt-2">Create and manage test marking guides by scanning answer keys</p>
                        </div>
                        <button onclick="App.showCreateMarkingGuideModal()"
                                class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md font-medium">
                            <i class="fas fa-camera mr-2"></i>Scan New Marking Guide
                        </button>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-lightbulb text-yellow-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">How to Create Marking Guides</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>1. Prepare your answer key with correct answers clearly written</p>
                                <p>2. Scan or photograph the answer key</p>
                                <p>3. Upload the scanned image and let our system extract the answers</p>
                                <p>4. Review and adjust the extracted answers if needed</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Marking Guides List -->
                <div class="bg-white shadow rounded-lg">
                    ${this.renderMarkingGuidesList(response.marking_guides)}
                </div>
            `;

            contentArea.innerHTML = content;

        } catch (error) {
            hideLoading();
            contentArea.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Marking Guides</h3>
                    <p class="text-gray-600">${error.message}</p>
                </div>
            `;
        }
    },

    // Render marking guides list
    renderMarkingGuidesList(guides) {
        if (!guides || guides.length === 0) {
            return `
                <div class="text-center py-12">
                    <i class="fas fa-clipboard-list text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Marking Guides Found</h3>
                    <p class="text-gray-600 mb-4">Create your first marking guide by scanning an answer key!</p>
                    <button onclick="App.showCreateMarkingGuideModal()"
                            class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md font-medium">
                        <i class="fas fa-camera mr-2"></i>Scan Answer Key
                    </button>
                </div>
            `;
        }

        const tableRows = guides.map(guide => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                <i class="fas fa-clipboard-list text-green-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${guide.title}</div>
                            <div class="text-sm text-gray-500">${guide.description || 'No description'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${guide.subject}</div>
                    <div class="text-sm text-gray-500">${guide.grade_level || 'No grade specified'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${guide.question_count} questions
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${guide.total_marks} marks
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${guide.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${guide.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${formatDate(guide.created_at)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button onclick="App.showMarkingGuideDetails(${guide.id})"
                            class="text-primary hover:text-secondary mr-3">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button onclick="App.editMarkingGuide(${guide.id})"
                            class="text-yellow-600 hover:text-yellow-800">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        return `
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guide</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Questions</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Marks</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        ${tableRows}
                    </tbody>
                </table>
            </div>
        `;
    },

    // Show create marking guide modal
    showCreateMarkingGuideModal() {
        const content = `
            <div class="space-y-6">
                <!-- Scan Instructions -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-green-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">Answer Key Scanning Instructions</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Prepare your answer key with correct answers clearly written</li>
                                    <li>Number each question clearly (1, 2, 3, etc.)</li>
                                    <li>Write answers legibly next to question numbers</li>
                                    <li>Use a scanner or smartphone camera for best quality</li>
                                    <li>Ensure good lighting and avoid shadows</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="marking-guide-form" class="space-y-4">
                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="guide-title" class="block text-sm font-medium text-gray-700">Test Title *</label>
                            <input type="text" id="guide-title" name="title" required
                                   placeholder="e.g., Math Quiz 1, Science Midterm"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label for="guide-subject" class="block text-sm font-medium text-gray-700">Subject *</label>
                            <input type="text" id="guide-subject" name="subject" required
                                   placeholder="e.g., Mathematics, Science, English"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="guide-grade" class="block text-sm font-medium text-gray-700">Grade Level</label>
                            <input type="text" id="guide-grade" name="grade_level"
                                   placeholder="e.g., Grade 10, Year 12, Form 4"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label for="guide-time" class="block text-sm font-medium text-gray-700">Time Limit (minutes)</label>
                            <input type="number" id="guide-time" name="time_limit"
                                   placeholder="e.g., 60, 90, 120"
                                   class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                    </div>

                    <div>
                        <label for="guide-description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea id="guide-description" name="description" rows="2"
                                  placeholder="Brief description of the test..."
                                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"></textarea>
                    </div>

                    <!-- Answer Key Upload -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                        <div class="space-y-4">
                            <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-image text-2xl text-gray-400"></i>
                            </div>
                            <div>
                                <label for="answer-key-file" class="cursor-pointer">
                                    <span class="text-lg font-medium text-gray-900">Scan & Upload Answer Key</span>
                                    <p class="text-sm text-gray-500 mt-1">Click to select scanned answer key or drag and drop</p>
                                </label>
                                <input type="file" id="answer-key-file" name="answer_key_file" accept="image/*,.pdf" required
                                       class="hidden" capture="environment">
                            </div>
                            <div class="flex justify-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-mobile-alt mr-1"></i>Phone Camera</span>
                                <span><i class="fas fa-scanner mr-1"></i>Scanner</span>
                                <span><i class="fas fa-file-image mr-1"></i>Image File</span>
                            </div>
                        </div>
                    </div>

                    <div id="answer-key-preview" class="file-preview"></div>
                    <div id="marking-guide-error" class="hidden text-red-600 text-sm"></div>
                </form>
            </div>
        `;

        const actions = [
            {
                text: 'Scan & Create Guide',
                class: 'bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md',
                onclick: 'App.handleCreateMarkingGuide()'
            }
        ];

        createModal('Create Marking Guide from Scanned Answer Key', content, actions);

        // Setup file preview with enhanced features
        const fileInput = document.getElementById('answer-key-file');
        const previewDiv = document.getElementById('answer-key-preview');
        handleFileUpload(fileInput, previewDiv, ['image/*', 'application/pdf']);

        // Add camera capture support if available
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            addCameraCapture(fileInput);
        }
    },

    // Show reports (placeholder)
    showReports(params = {}) {
        const contentArea = document.getElementById('content-area');
        contentArea.classList.remove('hidden');
        contentArea.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-chart-bar text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Reports</h3>
                <p class="text-gray-600">This feature is coming soon!</p>
            </div>
        `;
    },

    // Handle create marking guide
    async handleCreateMarkingGuide() {
        const form = document.getElementById('marking-guide-form');
        const errorDiv = document.getElementById('marking-guide-error');
        const formData = new FormData(form);

        // Validation
        if (!formData.get('title')) {
            this.showMarkingGuideError(errorDiv, 'Please enter a test title');
            return;
        }

        if (!formData.get('subject')) {
            this.showMarkingGuideError(errorDiv, 'Please enter a subject');
            return;
        }

        if (!formData.get('answer_key_file')) {
            this.showMarkingGuideError(errorDiv, 'Please upload a scanned answer key');
            return;
        }

        try {
            showLoading();

            // First scan the answer key to extract questions
            const scanFormData = new FormData();
            scanFormData.append('answer_key_file', formData.get('answer_key_file'));
            scanFormData.append('title', formData.get('title'));
            scanFormData.append('subject', formData.get('subject'));

            const scanResponse = await api.scanAnswerKey(scanFormData);

            if (scanResponse.questions && scanResponse.questions.length > 0) {
                // Show extracted questions for review
                hideLoading();
                closeModal();
                this.showExtractedQuestionsModal(formData, scanResponse);
            } else {
                // No questions extracted, create guide manually
                const guideData = {
                    title: formData.get('title'),
                    subject: formData.get('subject'),
                    description: formData.get('description'),
                    grade_level: formData.get('grade_level'),
                    time_limit: formData.get('time_limit') ? parseInt(formData.get('time_limit')) : null
                };

                const guideResponse = await api.createMarkingGuide(guideData);
                hideLoading();
                closeModal();

                showToast('Marking guide created! No questions were automatically detected. You can add them manually.', 'warning');
                this.showMarkingGuideDetails(guideResponse.marking_guide.id);
            }

        } catch (error) {
            hideLoading();
            this.showMarkingGuideError(errorDiv, error.message);
        }
    },

    // Show extracted questions modal for review
    showExtractedQuestionsModal(originalFormData, scanResponse) {
        const content = `
            <div class="space-y-6">
                <!-- Scan Results Summary -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">Answer Key Processed Successfully!</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <p><strong>Extracted:</strong> ${scanResponse.questions.length} questions</p>
                                <p><strong>OCR Confidence:</strong> ${scanResponse.confidence}%</p>
                                <p><strong>Estimated Total Marks:</strong> ${scanResponse.suggestions.estimated_marks}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Extracted Text Preview -->
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Extracted Text:</h4>
                    <div class="text-xs text-gray-600 max-h-32 overflow-y-auto bg-white p-2 rounded border">
                        ${scanResponse.extracted_text.replace(/\n/g, '<br>')}
                    </div>
                </div>

                <!-- Questions Review -->
                <div>
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Review Extracted Questions</h4>
                    <p class="text-sm text-gray-600 mb-4">Please review and edit the questions extracted from your answer key. You can modify the question text, type, correct answer, and marks.</p>

                    <div id="extracted-questions-list" class="space-y-4 max-h-96 overflow-y-auto">
                        ${this.renderExtractedQuestions(scanResponse.questions)}
                    </div>
                </div>

                <div id="extracted-questions-error" class="hidden text-red-600 text-sm"></div>
            </div>
        `;

        const actions = [
            {
                text: 'Create Marking Guide',
                class: 'bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md',
                onclick: 'App.finalizeMarkingGuideCreation()'
            }
        ];

        createModal('Review Extracted Questions', content, actions);

        // Store the original form data and scan response for later use
        window.markingGuideData = {
            originalFormData: originalFormData,
            scanResponse: scanResponse
        };
    },

    // Render extracted questions for review
    renderExtractedQuestions(questions) {
        return questions.map((question, index) => `
            <div class="border border-gray-200 rounded-lg p-4 bg-white">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Question ${question.question_number}</label>
                        <textarea class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                  rows="2" data-field="question_text" data-index="${index}">${question.question_text}</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Question Type</label>
                        <select class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                data-field="question_type" data-index="${index}">
                            <option value="multiple_choice" ${question.question_type === 'multiple_choice' ? 'selected' : ''}>Multiple Choice</option>
                            <option value="true_false" ${question.question_type === 'true_false' ? 'selected' : ''}>True/False</option>
                            <option value="short_answer" ${question.question_type === 'short_answer' ? 'selected' : ''}>Short Answer</option>
                            <option value="essay" ${question.question_type === 'essay' ? 'selected' : ''}>Essay</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Correct Answer</label>
                        <input type="text" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                               data-field="correct_answer" data-index="${index}" value="${question.correct_answer || ''}">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Marks</label>
                        <input type="number" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                               data-field="marks" data-index="${index}" value="${question.marks || 1}" min="1">
                    </div>
                </div>
                <div class="mt-3 flex items-center justify-between">
                    <span class="text-xs text-gray-500">
                        <i class="fas fa-robot mr-1"></i>
                        Confidence: ${Math.round(question.confidence * 100)}%
                    </span>
                    <button onclick="App.removeExtractedQuestion(${index})"
                            class="text-red-600 hover:text-red-800 text-sm">
                        <i class="fas fa-trash mr-1"></i>Remove
                    </button>
                </div>
            </div>
        `).join('');
    },

    // Remove extracted question
    removeExtractedQuestion(index) {
        const questionElement = document.querySelector(`[data-index="${index}"]`).closest('.border');
        questionElement.remove();
    },

    // Finalize marking guide creation
    async finalizeMarkingGuideCreation() {
        try {
            showLoading();

            const { originalFormData, scanResponse } = window.markingGuideData;

            // Create the marking guide first
            const guideData = {
                title: originalFormData.get('title'),
                subject: originalFormData.get('subject'),
                description: originalFormData.get('description'),
                grade_level: originalFormData.get('grade_level'),
                time_limit: originalFormData.get('time_limit') ? parseInt(originalFormData.get('time_limit')) : null
            };

            const guideResponse = await api.createMarkingGuide(guideData);
            const guideId = guideResponse.marking_guide.id;

            // Collect updated question data from the form
            const questionElements = document.querySelectorAll('[data-field="question_text"]');
            const questions = [];

            questionElements.forEach((element, index) => {
                const questionText = element.value;
                const questionType = document.querySelector(`[data-field="question_type"][data-index="${index}"]`).value;
                const correctAnswer = document.querySelector(`[data-field="correct_answer"][data-index="${index}"]`).value;
                const marks = parseInt(document.querySelector(`[data-field="marks"][data-index="${index}"]`).value) || 1;
                const questionNumber = index + 1;

                if (questionText.trim()) {
                    questions.push({
                        question_number: questionNumber,
                        question_text: questionText.trim(),
                        question_type: questionType,
                        correct_answer: correctAnswer.trim(),
                        marks: marks
                    });
                }
            });

            // Add all questions to the marking guide
            for (const question of questions) {
                await api.addQuestion(guideId, question);
            }

            hideLoading();
            closeModal();

            showToast(`Marking guide created successfully with ${questions.length} questions!`, 'success');

            // Clean up stored data
            delete window.markingGuideData;

            // Show the completed marking guide
            this.showMarkingGuideDetails(guideId);

        } catch (error) {
            hideLoading();
            const errorDiv = document.getElementById('extracted-questions-error');
            if (errorDiv) {
                errorDiv.innerHTML = error.message;
                errorDiv.classList.remove('hidden');
            } else {
                showToast(`Error creating marking guide: ${error.message}`, 'error');
            }
        }
    },

    // Show marking guide error
    showMarkingGuideError(errorDiv, message) {
        errorDiv.innerHTML = message;
        errorDiv.classList.remove('hidden');
    },

    // Show marking guide details
    async showMarkingGuideDetails(guideId) {
        if (!ApiHelpers.isAuthenticated()) return;

        const contentArea = document.getElementById('content-area');
        contentArea.classList.remove('hidden');

        try {
            showLoading();
            const response = await api.getMarkingGuide(guideId);
            hideLoading();

            const guide = response.marking_guide;

            const content = `
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-3xl font-bold text-gray-900">${guide.title}</h2>
                            <p class="text-gray-600 mt-2">${guide.subject} - ${guide.grade_level || 'No grade specified'}</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="App.showMarkingGuides()"
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Guides
                            </button>
                            <button onclick="App.addQuestionModal(${guide.id})"
                                    class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md">
                                <i class="fas fa-plus mr-2"></i>Add Question
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Guide Info -->
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Total Questions</h3>
                            <p class="text-2xl font-bold text-gray-900">${guide.question_count || 0}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Total Marks</h3>
                            <p class="text-2xl font-bold text-gray-900">${guide.total_marks || 0}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Time Limit</h3>
                            <p class="text-2xl font-bold text-gray-900">${guide.time_limit ? guide.time_limit + ' min' : 'No limit'}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Status</h3>
                            <p class="text-2xl font-bold ${guide.is_active ? 'text-green-600' : 'text-red-600'}">
                                ${guide.is_active ? 'Active' : 'Inactive'}
                            </p>
                        </div>
                    </div>
                    ${guide.description ? `<div class="mt-4"><p class="text-gray-600">${guide.description}</p></div>` : ''}
                </div>

                <!-- Questions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Questions</h3>
                    </div>
                    <div class="p-6">
                        ${this.renderQuestionsList(guide.questions || [])}
                    </div>
                </div>
            `;

            contentArea.innerHTML = content;

        } catch (error) {
            hideLoading();
            contentArea.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Error Loading Marking Guide</h3>
                    <p class="text-gray-600">${error.message}</p>
                </div>
            `;
        }
    },

    // Render questions list
    renderQuestionsList(questions) {
        if (!questions || questions.length === 0) {
            return `
                <div class="text-center py-8">
                    <i class="fas fa-question-circle text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Questions Added</h3>
                    <p class="text-gray-600 mb-4">Add questions to this marking guide to start using it for marking.</p>
                    <button onclick="App.addQuestionModal(${questions.marking_guide_id || 'null'})"
                            class="bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md font-medium">
                        <i class="fas fa-plus mr-2"></i>Add First Question
                    </button>
                </div>
            `;
        }

        const questionsHtml = questions.map(question => `
            <div class="border border-gray-200 rounded-lg p-4 mb-4">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <span class="bg-primary text-white px-2 py-1 rounded text-sm font-medium">Q${question.question_number}</span>
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${
                                question.question_type === 'multiple_choice' ? 'bg-blue-100 text-blue-800' :
                                question.question_type === 'short_answer' ? 'bg-green-100 text-green-800' :
                                question.question_type === 'essay' ? 'bg-purple-100 text-purple-800' :
                                'bg-gray-100 text-gray-800'
                            }">
                                ${question.question_type.replace('_', ' ').toUpperCase()}
                            </span>
                            <span class="text-sm text-gray-500">${question.marks} marks</span>
                        </div>
                        <p class="text-gray-900 mb-2">${question.question_text}</p>
                        ${question.correct_answer ? `<p class="text-sm text-green-600"><strong>Answer:</strong> ${question.correct_answer}</p>` : ''}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="App.editQuestion(${question.id})"
                                class="text-yellow-600 hover:text-yellow-800">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="App.deleteQuestion(${question.id})"
                                class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        return questionsHtml;
    },

    // Add question modal
    addQuestionModal(guideId) {
        const content = `
            <form id="add-question-form" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="question-number" class="block text-sm font-medium text-gray-700">Question Number *</label>
                        <input type="number" id="question-number" name="question_number" required min="1"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label for="question-marks" class="block text-sm font-medium text-gray-700">Marks *</label>
                        <input type="number" id="question-marks" name="marks" required min="1" value="1"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                </div>

                <div>
                    <label for="question-type" class="block text-sm font-medium text-gray-700">Question Type *</label>
                    <select id="question-type" name="question_type" required onchange="App.updateQuestionFields()"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        <option value="">Select question type...</option>
                        <option value="multiple_choice">Multiple Choice</option>
                        <option value="true_false">True/False</option>
                        <option value="short_answer">Short Answer</option>
                        <option value="essay">Essay</option>
                    </select>
                </div>

                <div>
                    <label for="question-text" class="block text-sm font-medium text-gray-700">Question Text *</label>
                    <textarea id="question-text" name="question_text" required rows="3"
                              placeholder="Enter the question text..."
                              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"></textarea>
                </div>

                <div id="answer-fields">
                    <!-- Dynamic answer fields will be inserted here -->
                </div>

                <div id="add-question-error" class="hidden text-red-600 text-sm"></div>
            </form>
        `;

        const actions = [
            {
                text: 'Add Question',
                class: 'bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md',
                onclick: `App.handleAddQuestion(${guideId})`
            }
        ];

        createModal('Add Question', content, actions);
    },

    // Update question fields based on type
    updateQuestionFields() {
        const questionType = document.getElementById('question-type').value;
        const answerFields = document.getElementById('answer-fields');

        switch (questionType) {
            case 'multiple_choice':
                answerFields.innerHTML = `
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Answer Options *</label>
                        <div class="space-y-2 mt-1">
                            <input type="text" name="option_a" placeholder="A) Option A" class="block w-full px-3 py-2 border border-gray-300 rounded-md">
                            <input type="text" name="option_b" placeholder="B) Option B" class="block w-full px-3 py-2 border border-gray-300 rounded-md">
                            <input type="text" name="option_c" placeholder="C) Option C" class="block w-full px-3 py-2 border border-gray-300 rounded-md">
                            <input type="text" name="option_d" placeholder="D) Option D" class="block w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                    </div>
                    <div>
                        <label for="correct-answer" class="block text-sm font-medium text-gray-700">Correct Answer *</label>
                        <select id="correct-answer" name="correct_answer" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">Select correct answer...</option>
                            <option value="A">A</option>
                            <option value="B">B</option>
                            <option value="C">C</option>
                            <option value="D">D</option>
                        </select>
                    </div>
                `;
                break;
            case 'true_false':
                answerFields.innerHTML = `
                    <div>
                        <label for="correct-answer" class="block text-sm font-medium text-gray-700">Correct Answer *</label>
                        <select id="correct-answer" name="correct_answer" required class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="">Select correct answer...</option>
                            <option value="True">True</option>
                            <option value="False">False</option>
                        </select>
                    </div>
                `;
                break;
            case 'short_answer':
                answerFields.innerHTML = `
                    <div>
                        <label for="correct-answer" class="block text-sm font-medium text-gray-700">Sample Correct Answer</label>
                        <input type="text" id="correct-answer" name="correct_answer"
                               placeholder="Enter a sample correct answer..."
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="keywords" class="block text-sm font-medium text-gray-700">Keywords (comma-separated)</label>
                        <input type="text" id="keywords" name="keywords"
                               placeholder="keyword1, keyword2, keyword3..."
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                `;
                break;
            case 'essay':
                answerFields.innerHTML = `
                    <div>
                        <label for="marking-criteria" class="block text-sm font-medium text-gray-700">Marking Criteria</label>
                        <textarea id="marking-criteria" name="marking_criteria" rows="3"
                                  placeholder="Describe what should be included in a good answer..."
                                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"></textarea>
                    </div>
                    <div>
                        <label for="keywords" class="block text-sm font-medium text-gray-700">Key Points/Keywords (comma-separated)</label>
                        <input type="text" id="keywords" name="keywords"
                               placeholder="key point 1, key point 2, important concept..."
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                `;
                break;
            default:
                answerFields.innerHTML = '';
        }
    },

    // Handle add question
    async handleAddQuestion(guideId) {
        const form = document.getElementById('add-question-form');
        const errorDiv = document.getElementById('add-question-error');
        const formData = new FormData(form);

        // Validation
        if (!formData.get('question_number') || !formData.get('question_text') || !formData.get('question_type') || !formData.get('marks')) {
            this.showQuestionError(errorDiv, 'Please fill in all required fields');
            return;
        }

        try {
            showLoading();

            const questionData = {
                question_number: parseInt(formData.get('question_number')),
                question_text: formData.get('question_text'),
                question_type: formData.get('question_type'),
                marks: parseInt(formData.get('marks')),
                correct_answer: formData.get('correct_answer') || '',
                marking_criteria: formData.get('marking_criteria') || ''
            };

            // Handle keywords
            const keywords = formData.get('keywords');
            if (keywords) {
                questionData.keywords = keywords.split(',').map(k => k.trim()).filter(k => k);
            }

            // Handle multiple choice options
            if (questionData.question_type === 'multiple_choice') {
                const options = [
                    formData.get('option_a'),
                    formData.get('option_b'),
                    formData.get('option_c'),
                    formData.get('option_d')
                ].filter(opt => opt && opt.trim());

                if (options.length > 0) {
                    questionData.answer_options = options;
                }
            }

            await api.addQuestion(guideId, questionData);

            hideLoading();
            closeModal();
            showToast('Question added successfully!', 'success');

            // Refresh the marking guide details
            this.showMarkingGuideDetails(guideId);

        } catch (error) {
            hideLoading();
            this.showQuestionError(errorDiv, error.message);
        }
    },

    // Show question error
    showQuestionError(errorDiv, message) {
        errorDiv.innerHTML = message;
        errorDiv.classList.remove('hidden');
    },

    // Generate individual report
    async generateIndividualReport(sheetId) {
        try {
            showLoading();
            const response = await api.generateIndividualReport(sheetId);
            hideLoading();

            showToast('Individual report generated successfully!', 'success');
            // Could navigate to report view or show modal with report

        } catch (error) {
            hideLoading();
            showToast(`Error generating report: ${error.message}`, 'error');
        }
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});
