# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=demo-secret-key-change-in-production
JWT_SECRET_KEY=demo-jwt-secret-key-change-in-production

# Database Configuration (SQLite)
DEV_DATABASE_URL=sqlite:///markease_dev.db
TEST_DATABASE_URL=sqlite:///markease_test.db
DATABASE_URL=sqlite:///markease.db

# OCR Configuration (Optional - will use basic text processing if not available)
# TESSERACT_CMD=tesseract

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
