#!/usr/bin/env python3
"""
Create a test user for debugging
"""

from app import create_app, db
from app.models.user import User, UserR<PERSON>

def create_test_user():
    """Create a test user."""
    app = create_app()
    
    with app.app_context():
        # Check if test user already exists
        existing_user = User.query.filter_by(username='testuser').first()
        if existing_user:
            print("Test user already exists!")
            print(f"Username: testuser")
            print(f"Email: <EMAIL>")
            print(f"Password: password123")
            return
        
        # Create test user
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            password='password123',
            first_name='Test',
            last_name='User',
            role=UserRole.TEACHER
        )
        
        db.session.add(test_user)
        db.session.commit()
        
        print("Test user created successfully!")
        print(f"Username: testuser")
        print(f"Email: <EMAIL>")
        print(f"Password: password123")

if __name__ == "__main__":
    create_test_user()
