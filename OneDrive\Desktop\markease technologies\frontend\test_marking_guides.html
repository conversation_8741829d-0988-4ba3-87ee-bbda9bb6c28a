<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Marking Guides - MarkEase</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Test Marking Guides Functionality</h1>
        
        <div class="space-y-6">
            <!-- Test Authentication -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">1. Test Authentication</h2>
                <div class="space-y-3">
                    <button onclick="testLogin()" class="bg-blue-500 text-white px-4 py-2 rounded">Test Login</button>
                    <button onclick="checkAuth()" class="bg-green-500 text-white px-4 py-2 rounded">Check Auth Status</button>
                    <div id="auth-result" class="text-sm text-gray-600"></div>
                </div>
            </div>

            <!-- Test API Endpoints -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">2. Test API Endpoints</h2>
                <div class="space-y-3">
                    <button onclick="testHealthCheck()" class="bg-purple-500 text-white px-4 py-2 rounded">Test Health Check</button>
                    <button onclick="testMarkingGuides()" class="bg-orange-500 text-white px-4 py-2 rounded">Test Marking Guides API</button>
                    <button onclick="testCreateGuide()" class="bg-red-500 text-white px-4 py-2 rounded">Test Create Guide</button>
                    <div id="api-result" class="text-sm text-gray-600 mt-3"></div>
                </div>
            </div>

            <!-- Test Frontend Functions -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">3. Test Frontend Functions</h2>
                <div class="space-y-3">
                    <button onclick="testShowMarkingGuides()" class="bg-indigo-500 text-white px-4 py-2 rounded">Test Show Marking Guides</button>
                    <button onclick="testCreateModal()" class="bg-pink-500 text-white px-4 py-2 rounded">Test Create Modal</button>
                    <div id="frontend-result" class="text-sm text-gray-600 mt-3"></div>
                </div>
            </div>

            <!-- Console Output -->
            <div class="bg-gray-900 text-green-400 p-4 rounded-lg">
                <h3 class="text-lg font-semibold mb-2">Console Output:</h3>
                <div id="console-output" class="text-xs font-mono whitespace-pre-wrap max-h-64 overflow-y-auto"></div>
            </div>
        </div>
    </div>

    <!-- Include the main scripts -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/app.js"></script>

    <script>
        // Override console.log to show in our output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : 'text-green-400';
            consoleOutput.innerHTML += `<span class="${color}">[${timestamp}] ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // Test functions
        async function testLogin() {
            const result = document.getElementById('auth-result');
            try {
                console.log('Testing login...');
                const response = await api.login('testuser', 'password123');
                result.innerHTML = `<span class="text-green-600">Login test: Success! Token received.</span>`;
                console.log('Login successful, token stored');
            } catch (error) {
                console.error('Login error:', error);
                result.innerHTML = `<span class="text-red-600">Login error: ${error.message}</span>`;
            }
        }

        function checkAuth() {
            const result = document.getElementById('auth-result');
            const isAuth = ApiHelpers.isAuthenticated();
            const token = localStorage.getItem('access_token');
            result.innerHTML = `<span class="text-blue-600">Authenticated: ${isAuth}, Token: ${token ? 'Present' : 'None'}</span>`;
            console.log('Auth check:', { isAuth, token: token ? 'Present' : 'None' });
        }

        async function testHealthCheck() {
            const result = document.getElementById('api-result');
            try {
                console.log('Testing health check...');
                const response = await api.healthCheck();
                result.innerHTML = `<span class="text-green-600">Health check: ${JSON.stringify(response)}</span>`;
            } catch (error) {
                console.error('Health check error:', error);
                result.innerHTML = `<span class="text-red-600">Health check error: ${error.message}</span>`;
            }
        }

        async function testMarkingGuides() {
            const result = document.getElementById('api-result');
            try {
                console.log('Testing marking guides API...');
                const response = await api.getMarkingGuides();
                result.innerHTML = `<span class="text-green-600">Marking guides: ${JSON.stringify(response)}</span>`;
            } catch (error) {
                console.error('Marking guides error:', error);
                result.innerHTML = `<span class="text-red-600">Marking guides error: ${error.message}</span>`;
            }
        }

        async function testCreateGuide() {
            const result = document.getElementById('api-result');
            try {
                console.log('Testing create guide...');
                const response = await api.createMarkingGuide({
                    title: 'Test Guide',
                    subject: 'Test Subject',
                    description: 'Test Description'
                });
                result.innerHTML = `<span class="text-green-600">Create guide: ${JSON.stringify(response)}</span>`;
            } catch (error) {
                console.error('Create guide error:', error);
                result.innerHTML = `<span class="text-red-600">Create guide error: ${error.message}</span>`;
            }
        }

        function testShowMarkingGuides() {
            const result = document.getElementById('frontend-result');
            try {
                console.log('Testing showMarkingGuides function...');
                if (typeof App !== 'undefined' && App.showMarkingGuides) {
                    App.showMarkingGuides();
                    result.innerHTML = `<span class="text-green-600">showMarkingGuides function called successfully</span>`;
                } else {
                    result.innerHTML = `<span class="text-red-600">App.showMarkingGuides function not found</span>`;
                }
            } catch (error) {
                console.error('showMarkingGuides error:', error);
                result.innerHTML = `<span class="text-red-600">showMarkingGuides error: ${error.message}</span>`;
            }
        }

        function testCreateModal() {
            const result = document.getElementById('frontend-result');
            try {
                console.log('Testing create modal...');
                if (typeof App !== 'undefined' && App.showCreateMarkingGuideModal) {
                    App.showCreateMarkingGuideModal();
                    result.innerHTML = `<span class="text-green-600">Create modal function called successfully</span>`;
                } else {
                    result.innerHTML = `<span class="text-red-600">App.showCreateMarkingGuideModal function not found</span>`;
                }
            } catch (error) {
                console.error('Create modal error:', error);
                result.innerHTML = `<span class="text-red-600">Create modal error: ${error.message}</span>`;
            }
        }

        // Initialize
        console.log('Test page loaded');
        console.log('API object:', typeof api);
        console.log('App object:', typeof App);
        console.log('Auth object:', typeof Auth);
    </script>
</body>
</html>
