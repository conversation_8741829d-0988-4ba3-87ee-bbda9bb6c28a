from app import db
from datetime import datetime
from enum import Enum

class QuestionType(Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    SHORT_ANSWER = "short_answer"
    ESSAY = "essay"
    TRUE_FALSE = "true_false"

class MarkingGuide(db.Model):
    """Marking guide model for storing test templates and marking criteria."""
    __tablename__ = 'marking_guides'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    subject = db.Column(db.String(100), nullable=False)
    grade_level = db.Column(db.String(20))
    total_marks = db.Column(db.Integer, nullable=False, default=0)
    time_limit = db.Column(db.Integer)  # in minutes
    instructions = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Foreign Keys
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Relationships
    questions = db.relationship('Question', backref='marking_guide', lazy='dynamic', cascade='all, delete-orphan')
    answer_sheets = db.relationship('AnswerSheet', backref='marking_guide', lazy='dynamic')
    
    def __init__(self, title, subject, teacher_id, description=None, grade_level=None, 
                 time_limit=None, instructions=None):
        self.title = title
        self.description = description
        self.subject = subject
        self.grade_level = grade_level
        self.time_limit = time_limit
        self.instructions = instructions
        self.teacher_id = teacher_id
    
    def calculate_total_marks(self):
        """Calculate total marks from all questions."""
        total = sum(question.marks for question in self.questions)
        self.total_marks = total
        return total
    
    def to_dict(self):
        """Convert marking guide object to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'subject': self.subject,
            'grade_level': self.grade_level,
            'total_marks': self.total_marks,
            'time_limit': self.time_limit,
            'instructions': self.instructions,
            'is_active': self.is_active,
            'teacher_id': self.teacher_id,
            'teacher_name': self.teacher.full_name if self.teacher else None,
            'question_count': self.questions.count(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<MarkingGuide {self.title}>'
