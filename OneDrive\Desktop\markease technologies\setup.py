#!/usr/bin/env python3
"""
MarkEase Setup Script
This script helps set up the MarkEase application environment.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is 3.8 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_mysql():
    """Check if MySQL is available."""
    try:
        result = subprocess.run("mysql --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ MySQL is available")
            return True
        else:
            print("✗ MySQL not found. Please install MySQL 8.0+")
            return False
    except:
        print("✗ MySQL not found. Please install MySQL 8.0+")
        return False

def create_virtual_environment():
    """Create Python virtual environment."""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✓ Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def activate_venv_command():
    """Get the command to activate virtual environment based on OS."""
    if os.name == 'nt':  # Windows
        return "venv\\Scripts\\activate"
    else:  # Unix/Linux/macOS
        return "source venv/bin/activate"

def install_python_dependencies():
    """Install Python dependencies."""
    if os.name == 'nt':  # Windows
        pip_command = "venv\\Scripts\\pip install -r backend\\requirements.txt"
    else:  # Unix/Linux/macOS
        pip_command = "venv/bin/pip install -r backend/requirements.txt"
    
    return run_command(pip_command, "Installing Python dependencies")

def setup_database():
    """Setup database."""
    print("\n" + "="*50)
    print("DATABASE SETUP")
    print("="*50)
    
    print("\nPlease ensure MySQL is running and you have admin access.")
    
    # Get database credentials
    db_user = input("Enter MySQL username (default: root): ").strip() or "root"
    db_password = input("Enter MySQL password: ").strip()
    db_host = input("Enter MySQL host (default: localhost): ").strip() or "localhost"
    
    # Create databases
    databases = ["markease_dev", "markease_test", "markease"]
    
    for db_name in databases:
        command = f'mysql -h {db_host} -u {db_user} -p{db_password} -e "CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"'
        if run_command(command, f"Creating database {db_name}"):
            print(f"✓ Database {db_name} created successfully")
        else:
            print(f"✗ Failed to create database {db_name}")
    
    # Import schema
    schema_file = "database/schema.sql"
    if os.path.exists(schema_file):
        command = f'mysql -h {db_host} -u {db_user} -p{db_password} markease_dev < {schema_file}'
        run_command(command, "Importing database schema")
    
    return db_user, db_password, db_host

def create_env_file(db_user, db_password, db_host):
    """Create .env file with configuration."""
    env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-in-production

# Database Configuration
DEV_DATABASE_URL=mysql+pymysql://{db_user}:{db_password}@{db_host}/markease_dev
TEST_DATABASE_URL=mysql+pymysql://{db_user}:{db_password}@{db_host}/markease_test
DATABASE_URL=mysql+pymysql://{db_user}:{db_password}@{db_host}/markease

# OCR Configuration (Update path as needed)
TESSERACT_CMD=C:\\Program Files\\Tesseract-OCR\\tesseract.exe

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
"""
    
    env_file = "backend/.env"
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✓ Created {env_file}")
    print("⚠️  Please update the TESSERACT_CMD path in .env file if needed")

def create_uploads_directory():
    """Create uploads directory."""
    uploads_dir = Path("backend/uploads")
    uploads_dir.mkdir(exist_ok=True)
    print("✓ Created uploads directory")

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*50)
    print("SETUP COMPLETE!")
    print("="*50)
    
    print("\nNext steps:")
    print("1. Install Tesseract OCR:")
    print("   - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
    print("   - macOS: brew install tesseract")
    print("   - Ubuntu: sudo apt install tesseract-ocr")
    
    print("\n2. Update the TESSERACT_CMD path in backend/.env if needed")
    
    print("\n3. Start the backend server:")
    if os.name == 'nt':  # Windows
        print("   cd backend")
        print("   ..\\venv\\Scripts\\activate")
        print("   python app.py")
    else:
        print("   cd backend")
        print("   source ../venv/bin/activate")
        print("   python app.py")
    
    print("\n4. Open frontend/pages/index.html in your web browser")
    
    print("\n5. Register a new teacher account and start using MarkEase!")
    
    print("\nFor more information, see README.md")

def main():
    """Main setup function."""
    print("="*50)
    print("MARKEASE SETUP")
    print("="*50)
    
    # Check prerequisites
    print("\nChecking prerequisites...")
    if not check_python_version():
        sys.exit(1)
    
    if not check_mysql():
        print("Please install MySQL and try again.")
        sys.exit(1)
    
    # Setup Python environment
    print("\nSetting up Python environment...")
    if not create_virtual_environment():
        sys.exit(1)
    
    if not install_python_dependencies():
        sys.exit(1)
    
    # Setup database
    db_user, db_password, db_host = setup_database()
    
    # Create configuration files
    print("\nCreating configuration files...")
    create_env_file(db_user, db_password, db_host)
    create_uploads_directory()
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
