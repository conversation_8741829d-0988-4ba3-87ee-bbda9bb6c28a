from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.answer_sheet import AnswerSheet
from app.models.marking_guide import MarkingGuide
from app.models.report import Report, ReportType
from app.models.answer import Answer
from app.models.question import Question
from sqlalchemy import func
from datetime import datetime, timedelta
import json

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('', methods=['GET'])
@reports_bp.route('/', methods=['GET'])
@jwt_required()
def get_reports():
    """Get all reports for the current teacher."""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        report_type = request.args.get('report_type')
        
        # Build query
        query = Report.query.filter_by(teacher_id=user_id)
        
        if report_type:
            try:
                report_type_enum = ReportType(report_type)
                query = query.filter_by(report_type=report_type_enum)
            except ValueError:
                return jsonify({'error': 'Invalid report type'}), 400
        
        # Order by creation date (newest first)
        query = query.order_by(Report.created_at.desc())
        
        # Paginate
        reports = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'reports': [report.to_dict() for report in reports.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': reports.total,
                'pages': reports.pages,
                'has_next': reports.has_next,
                'has_prev': reports.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/individual/<int:answer_sheet_id>', methods=['POST'])
@jwt_required()
def generate_individual_report(answer_sheet_id):
    """Generate individual student report."""
    try:
        user_id = int(get_jwt_identity())
        
        # Verify answer sheet belongs to teacher
        answer_sheet = AnswerSheet.query.filter_by(
            id=answer_sheet_id, teacher_id=user_id
        ).first()
        
        if not answer_sheet:
            return jsonify({'error': 'Answer sheet not found'}), 404
        
        # Get answers with questions
        answers = Answer.query.filter_by(
            answer_sheet_id=answer_sheet_id
        ).join(Question).order_by(Question.question_number).all()
        
        # Calculate statistics
        total_questions = len(answers)
        correct_answers = sum(1 for answer in answers if answer.is_correct)
        total_marks_possible = sum(answer.question.marks for answer in answers)
        total_marks_obtained = sum(answer.marks_obtained for answer in answers)
        
        # Question type breakdown
        question_types = {}
        for answer in answers:
            q_type = answer.question.question_type.value
            if q_type not in question_types:
                question_types[q_type] = {
                    'total': 0,
                    'correct': 0,
                    'marks_possible': 0,
                    'marks_obtained': 0
                }
            
            question_types[q_type]['total'] += 1
            if answer.is_correct:
                question_types[q_type]['correct'] += 1
            question_types[q_type]['marks_possible'] += answer.question.marks
            question_types[q_type]['marks_obtained'] += answer.marks_obtained
        
        # Prepare report data
        report_data = {
            'student_info': {
                'name': answer_sheet.student_name,
                'student_id': answer_sheet.student_id,
                'class_name': answer_sheet.class_name,
                'exam_date': answer_sheet.exam_date.isoformat() if answer_sheet.exam_date else None
            },
            'test_info': {
                'title': answer_sheet.marking_guide.title,
                'subject': answer_sheet.marking_guide.subject,
                'total_questions': total_questions,
                'total_marks': total_marks_possible
            },
            'performance': {
                'total_marks_obtained': total_marks_obtained,
                'percentage_score': answer_sheet.percentage_score,
                'grade': answer_sheet.get_grade(),
                'correct_answers': correct_answers,
                'incorrect_answers': total_questions - correct_answers
            },
            'question_breakdown': question_types,
            'detailed_answers': [
                {
                    'question_number': answer.question.question_number,
                    'question_text': answer.question.question_text,
                    'question_type': answer.question.question_type.value,
                    'marks_possible': answer.question.marks,
                    'student_answer': answer.student_answer,
                    'marks_obtained': answer.marks_obtained,
                    'is_correct': answer.is_correct,
                    'feedback': answer.reviewer_notes
                }
                for answer in answers
            ],
            'generated_at': datetime.utcnow().isoformat()
        }
        
        # Create report record
        report = Report(
            title=f"Individual Report - {answer_sheet.student_name}",
            report_type=ReportType.INDIVIDUAL,
            teacher_id=user_id,
            answer_sheet_id=answer_sheet_id,
            summary=f"Score: {answer_sheet.percentage_score:.1f}% ({answer_sheet.get_grade()})"
        )
        report.set_report_data(report_data)
        
        db.session.add(report)
        db.session.commit()
        
        return jsonify({
            'message': 'Individual report generated successfully',
            'report': report.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/class-summary/<int:marking_guide_id>', methods=['POST'])
@jwt_required()
def generate_class_summary(marking_guide_id):
    """Generate class summary report."""
    try:
        user_id = int(get_jwt_identity())
        
        # Verify marking guide belongs to teacher
        marking_guide = MarkingGuide.query.filter_by(
            id=marking_guide_id, teacher_id=user_id
        ).first()
        
        if not marking_guide:
            return jsonify({'error': 'Marking guide not found'}), 404
        
        # Get all answer sheets for this marking guide
        answer_sheets = AnswerSheet.query.filter_by(
            marking_guide_id=marking_guide_id,
            teacher_id=user_id
        ).all()
        
        if not answer_sheets:
            return jsonify({'error': 'No answer sheets found for this marking guide'}), 404
        
        # Calculate class statistics
        total_students = len(answer_sheets)
        scores = [sheet.percentage_score for sheet in answer_sheets]
        
        class_stats = {
            'total_students': total_students,
            'average_score': sum(scores) / total_students,
            'highest_score': max(scores),
            'lowest_score': min(scores),
            'median_score': sorted(scores)[total_students // 2],
            'pass_rate': sum(1 for score in scores if score >= 50) / total_students * 100
        }
        
        # Grade distribution
        grade_distribution = {}
        for sheet in answer_sheets:
            grade = sheet.get_grade()
            grade_distribution[grade] = grade_distribution.get(grade, 0) + 1
        
        # Question analysis
        questions = Question.query.filter_by(marking_guide_id=marking_guide_id).all()
        question_analysis = []
        
        for question in questions:
            answers = Answer.query.join(AnswerSheet).filter(
                Answer.question_id == question.id,
                AnswerSheet.marking_guide_id == marking_guide_id,
                AnswerSheet.teacher_id == user_id
            ).all()
            
            if answers:
                correct_count = sum(1 for answer in answers if answer.is_correct)
                avg_marks = sum(answer.marks_obtained for answer in answers) / len(answers)
                
                question_analysis.append({
                    'question_number': question.question_number,
                    'question_text': question.question_text[:100] + '...' if len(question.question_text) > 100 else question.question_text,
                    'question_type': question.question_type.value,
                    'total_marks': question.marks,
                    'students_attempted': len(answers),
                    'students_correct': correct_count,
                    'success_rate': (correct_count / len(answers)) * 100,
                    'average_marks': round(avg_marks, 2)
                })
        
        # Student performance list
        student_performance = [
            {
                'student_name': sheet.student_name,
                'student_id': sheet.student_id,
                'total_score': sheet.total_score,
                'percentage_score': sheet.percentage_score,
                'grade': sheet.get_grade(),
                'rank': 0  # Will be calculated after sorting
            }
            for sheet in answer_sheets
        ]
        
        # Sort by percentage score and assign ranks
        student_performance.sort(key=lambda x: x['percentage_score'], reverse=True)
        for i, student in enumerate(student_performance):
            student['rank'] = i + 1
        
        # Prepare report data
        report_data = {
            'test_info': {
                'title': marking_guide.title,
                'subject': marking_guide.subject,
                'total_questions': len(questions),
                'total_marks': marking_guide.total_marks
            },
            'class_statistics': class_stats,
            'grade_distribution': grade_distribution,
            'question_analysis': question_analysis,
            'student_performance': student_performance,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        # Create report record
        report = Report(
            title=f"Class Summary - {marking_guide.title}",
            report_type=ReportType.CLASS_SUMMARY,
            teacher_id=user_id,
            summary=f"Class Average: {class_stats['average_score']:.1f}% ({total_students} students)"
        )
        report.set_report_data(report_data)
        
        db.session.add(report)
        db.session.commit()
        
        return jsonify({
            'message': 'Class summary report generated successfully',
            'report': report.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/<int:report_id>', methods=['GET'])
@jwt_required()
def get_report(report_id):
    """Get a specific report."""
    try:
        user_id = int(get_jwt_identity())
        
        report = Report.query.filter_by(
            id=report_id, teacher_id=user_id
        ).first()
        
        if not report:
            return jsonify({'error': 'Report not found'}), 404
        
        return jsonify({
            'report': report.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
