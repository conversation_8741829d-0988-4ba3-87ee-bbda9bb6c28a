from app import db
from datetime import datetime
from enum import Enum

class ProcessingStatus(Enum):
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    MARKED = "marked"
    ERROR = "error"

class AnswerSheet(db.Model):
    """Answer sheet model for storing uploaded student answer sheets."""
    __tablename__ = 'answer_sheets'
    
    id = db.Column(db.Integer, primary_key=True)
    student_name = db.Column(db.String(100), nullable=False)
    student_id = db.Column(db.String(50))
    class_name = db.Column(db.String(50))
    exam_date = db.Column(db.Date)
    file_path = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_size = db.Column(db.Integer)
    processing_status = db.Column(db.Enum(ProcessingStatus), default=ProcessingStatus.UPLOADED, nullable=False)
    ocr_text = db.Column(db.Text)  # Extracted text from OCR
    total_score = db.Column(db.Float, default=0.0)
    percentage_score = db.Column(db.Float, default=0.0)
    processing_notes = db.Column(db.Text)  # Any notes from processing
    is_reviewed = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    processed_at = db.Column(db.DateTime)
    
    # Foreign Keys
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    marking_guide_id = db.Column(db.Integer, db.ForeignKey('marking_guides.id'), nullable=False)
    
    # Relationships
    answers = db.relationship('Answer', backref='answer_sheet', lazy='dynamic', cascade='all, delete-orphan')
    reports = db.relationship('Report', backref='answer_sheet', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, student_name, file_path, original_filename, teacher_id, 
                 marking_guide_id, student_id=None, class_name=None, exam_date=None, file_size=None):
        self.student_name = student_name
        self.student_id = student_id
        self.class_name = class_name
        self.exam_date = exam_date
        self.file_path = file_path
        self.original_filename = original_filename
        self.file_size = file_size
        self.teacher_id = teacher_id
        self.marking_guide_id = marking_guide_id
    
    def calculate_scores(self):
        """Calculate total score and percentage from answers."""
        total_obtained = sum(answer.marks_obtained for answer in self.answers if answer.marks_obtained)
        self.total_score = total_obtained
        
        if self.marking_guide and self.marking_guide.total_marks > 0:
            self.percentage_score = (total_obtained / self.marking_guide.total_marks) * 100
        else:
            self.percentage_score = 0.0
        
        return self.total_score, self.percentage_score
    
    def update_processing_status(self, status, notes=None):
        """Update processing status and notes."""
        self.processing_status = status
        if notes:
            self.processing_notes = notes
        if status == ProcessingStatus.PROCESSED:
            self.processed_at = datetime.utcnow()
    
    def get_grade(self):
        """Get letter grade based on percentage score."""
        if self.percentage_score >= 90:
            return 'A+'
        elif self.percentage_score >= 85:
            return 'A'
        elif self.percentage_score >= 80:
            return 'A-'
        elif self.percentage_score >= 75:
            return 'B+'
        elif self.percentage_score >= 70:
            return 'B'
        elif self.percentage_score >= 65:
            return 'B-'
        elif self.percentage_score >= 60:
            return 'C+'
        elif self.percentage_score >= 55:
            return 'C'
        elif self.percentage_score >= 50:
            return 'C-'
        elif self.percentage_score >= 45:
            return 'D'
        else:
            return 'F'
    
    def to_dict(self):
        """Convert answer sheet object to dictionary."""
        return {
            'id': self.id,
            'student_name': self.student_name,
            'student_id': self.student_id,
            'class_name': self.class_name,
            'exam_date': self.exam_date.isoformat() if self.exam_date else None,
            'file_path': self.file_path,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'processing_status': self.processing_status.value,
            'total_score': self.total_score,
            'percentage_score': round(self.percentage_score, 2),
            'grade': self.get_grade(),
            'processing_notes': self.processing_notes,
            'is_reviewed': self.is_reviewed,
            'teacher_id': self.teacher_id,
            'marking_guide_id': self.marking_guide_id,
            'marking_guide_title': self.marking_guide.title if self.marking_guide else None,
            'answer_count': self.answers.count(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'processed_at': self.processed_at.isoformat() if self.processed_at else None
        }
    
    def __repr__(self):
        return f'<AnswerSheet {self.student_name} - {self.original_filename}>'
