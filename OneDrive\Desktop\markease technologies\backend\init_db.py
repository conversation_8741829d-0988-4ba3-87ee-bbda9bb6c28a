#!/usr/bin/env python3
"""
Initialize the MarkEase database
"""

from app import create_app, db

def init_database():
    """Initialize the database with tables."""
    app = create_app()
    
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database initialized successfully!")
        
        # Print some info
        print(f"Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
        print("Tables created:")
        for table in db.metadata.tables.keys():
            print(f"  - {table}")

if __name__ == "__main__":
    init_database()
