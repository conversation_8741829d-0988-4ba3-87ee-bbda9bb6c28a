from app import db
from datetime import datetime
from enum import Enum
import json

class QuestionType(Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    SHORT_ANSWER = "short_answer"
    ESSAY = "essay"
    TRUE_FALSE = "true_false"

class Question(db.Model):
    """Question model for storing individual questions in marking guides."""
    __tablename__ = 'questions'
    
    id = db.Column(db.Integer, primary_key=True)
    question_number = db.Column(db.Integer, nullable=False)
    question_text = db.Column(db.Text, nullable=False)
    question_type = db.Column(db.Enum(QuestionType), nullable=False)
    marks = db.Column(db.Integer, nullable=False, default=1)
    correct_answer = db.Column(db.Text)  # For MCQ and True/False
    answer_options = db.Column(db.Text)  # JSON string for MCQ options
    marking_criteria = db.Column(db.Text)  # For essay and short answer questions
    keywords = db.Column(db.Text)  # JSON string of keywords for automated marking
    case_sensitive = db.Column(db.<PERSON>, default=False)
    partial_marks = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Foreign Keys
    marking_guide_id = db.Column(db.Integer, db.ForeignKey('marking_guides.id'), nullable=False)
    
    # Relationships
    answers = db.relationship('Answer', backref='question', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, question_number, question_text, question_type, marks, 
                 marking_guide_id, correct_answer=None, answer_options=None, 
                 marking_criteria=None, keywords=None):
        self.question_number = question_number
        self.question_text = question_text
        self.question_type = question_type
        self.marks = marks
        self.marking_guide_id = marking_guide_id
        self.correct_answer = correct_answer
        self.answer_options = answer_options
        self.marking_criteria = marking_criteria
        self.keywords = keywords
    
    def get_answer_options(self):
        """Get answer options as a list."""
        if self.answer_options:
            try:
                return json.loads(self.answer_options)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_answer_options(self, options):
        """Set answer options from a list."""
        if options:
            self.answer_options = json.dumps(options)
        else:
            self.answer_options = None
    
    def get_keywords(self):
        """Get keywords as a list."""
        if self.keywords:
            try:
                return json.loads(self.keywords)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_keywords(self, keywords):
        """Set keywords from a list."""
        if keywords:
            self.keywords = json.dumps(keywords)
        else:
            self.keywords = None
    
    def to_dict(self):
        """Convert question object to dictionary."""
        return {
            'id': self.id,
            'question_number': self.question_number,
            'question_text': self.question_text,
            'question_type': self.question_type.value,
            'marks': self.marks,
            'correct_answer': self.correct_answer,
            'answer_options': self.get_answer_options(),
            'marking_criteria': self.marking_criteria,
            'keywords': self.get_keywords(),
            'case_sensitive': self.case_sensitive,
            'partial_marks': self.partial_marks,
            'marking_guide_id': self.marking_guide_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Question {self.question_number}: {self.question_text[:50]}...>'
