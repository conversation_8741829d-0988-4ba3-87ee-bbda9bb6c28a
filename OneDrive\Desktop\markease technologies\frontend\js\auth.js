// Authentication module for MarkEase application

const Auth = {
    // Initialize authentication
    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
    },

    // Setup event listeners
    setupEventListeners() {
        // Login button
        document.getElementById('login-btn')?.addEventListener('click', () => {
            this.showLoginModal();
        });

        // Register button
        document.getElementById('register-btn')?.addEventListener('click', () => {
            this.showRegisterModal();
        });

        // Logout button
        document.getElementById('logout-btn')?.addEventListener('click', () => {
            this.logout();
        });
    },

    // Check authentication status
    checkAuthStatus() {
        const token = localStorage.getItem('access_token');
        const userData = localStorage.getItem('user_data');

        if (token && userData) {
            this.setAuthenticatedState(JSON.parse(userData));
        } else {
            this.setUnauthenticatedState();
        }
    },

    // Set authenticated state
    setAuthenticatedState(userData) {
        // Hide auth buttons and welcome section
        document.getElementById('auth-buttons').classList.add('hidden');
        document.getElementById('welcome-section').classList.add('hidden');
        
        // Show user menu and dashboard
        document.getElementById('user-menu').classList.remove('hidden');
        document.getElementById('dashboard-section').classList.remove('hidden');
        
        // Update user name
        document.getElementById('user-name').textContent = userData.full_name || userData.username;
        
        // Update navigation
        this.updateNavigation(true);
        
        // Load dashboard data
        if (window.Dashboard) {
            Dashboard.loadDashboardData();
        }
    },

    // Set unauthenticated state
    setUnauthenticatedState() {
        // Show auth buttons and welcome section
        document.getElementById('auth-buttons').classList.remove('hidden');
        document.getElementById('welcome-section').classList.remove('hidden');
        
        // Hide user menu and dashboard
        document.getElementById('user-menu').classList.add('hidden');
        document.getElementById('dashboard-section').classList.add('hidden');
        document.getElementById('content-area').classList.add('hidden');
        
        // Update navigation
        this.updateNavigation(false);
    },

    // Update navigation based on auth status
    updateNavigation(isAuthenticated) {
        const mainNav = document.getElementById('main-nav');
        
        if (isAuthenticated) {
            mainNav.innerHTML = `
                <a href="#" onclick="App.showDashboard()" class="text-gray-900 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                <a href="#" onclick="App.showAnswerSheets()" class="text-gray-500 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Answer Sheets</a>
                <a href="#" onclick="App.showMarkingGuides()" class="text-gray-500 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Marking Guides</a>
                <a href="#" onclick="App.showReports()" class="text-gray-500 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Reports</a>
            `;
        } else {
            mainNav.innerHTML = '';
        }
    },

    // Show login modal
    showLoginModal() {
        const content = `
            <form id="login-form" class="space-y-4">
                <div>
                    <label for="login-username" class="block text-sm font-medium text-gray-700">Username or Email</label>
                    <input type="text" id="login-username" name="username" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                </div>
                <div>
                    <label for="login-password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" id="login-password" name="password" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                </div>
                <div id="login-error" class="hidden text-red-600 text-sm"></div>
            </form>
        `;

        const actions = [
            {
                text: 'Login',
                class: 'bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md',
                onclick: 'Auth.handleLogin()'
            }
        ];

        createModal('Login to MarkEase', content, actions);

        // Handle form submission
        document.getElementById('login-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
    },

    // Show register modal
    showRegisterModal() {
        const content = `
            <form id="register-form" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="register-first-name" class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" id="register-first-name" name="first_name" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label for="register-last-name" class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" id="register-last-name" name="last_name" required 
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                </div>
                <div>
                    <label for="register-username" class="block text-sm font-medium text-gray-700">Username</label>
                    <input type="text" id="register-username" name="username" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                </div>
                <div>
                    <label for="register-email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" id="register-email" name="email" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                </div>
                <div>
                    <label for="register-password" class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" id="register-password" name="password" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                    <p class="mt-1 text-xs text-gray-500">Password must be at least 8 characters with uppercase, lowercase, and numbers</p>
                </div>
                <div>
                    <label for="register-confirm-password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input type="password" id="register-confirm-password" name="confirm_password" required 
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                </div>
                <div id="register-error" class="hidden text-red-600 text-sm"></div>
            </form>
        `;

        const actions = [
            {
                text: 'Register',
                class: 'bg-primary hover:bg-secondary text-white px-4 py-2 rounded-md',
                onclick: 'Auth.handleRegister()'
            }
        ];

        createModal('Register for MarkEase', content, actions);

        // Handle form submission
        document.getElementById('register-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });
    },

    // Handle login
    async handleLogin() {
        const form = document.getElementById('login-form');
        const errorDiv = document.getElementById('login-error');
        const formData = new FormData(form);

        const username = formData.get('username');
        const password = formData.get('password');

        if (!username || !password) {
            this.showError(errorDiv, 'Please fill in all fields');
            return;
        }

        try {
            showLoading();
            const response = await api.login(username, password);
            hideLoading();

            // Store token and user data
            api.setToken(response.access_token);
            ApiHelpers.setUserData(response.user);

            // Update UI
            this.setAuthenticatedState(response.user);
            closeModal();
            showToast('Login successful!', 'success');

        } catch (error) {
            hideLoading();
            this.showError(errorDiv, error.message);
        }
    },

    // Handle registration
    async handleRegister() {
        const form = document.getElementById('register-form');
        const errorDiv = document.getElementById('register-error');
        const formData = new FormData(form);

        const userData = {
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password')
        };

        const confirmPassword = formData.get('confirm_password');

        // Validation
        if (!userData.first_name || !userData.last_name || !userData.username || !userData.email || !userData.password) {
            this.showError(errorDiv, 'Please fill in all fields');
            return;
        }

        if (!validateEmail(userData.email)) {
            this.showError(errorDiv, 'Please enter a valid email address');
            return;
        }

        const passwordValidation = validatePassword(userData.password);
        if (!passwordValidation.isValid) {
            this.showError(errorDiv, passwordValidation.errors.join('<br>'));
            return;
        }

        if (userData.password !== confirmPassword) {
            this.showError(errorDiv, 'Passwords do not match');
            return;
        }

        try {
            showLoading();
            const response = await api.register(userData);
            hideLoading();

            // Store token and user data
            api.setToken(response.access_token);
            ApiHelpers.setUserData(response.user);

            // Update UI
            this.setAuthenticatedState(response.user);
            closeModal();
            showToast('Registration successful! Welcome to MarkEase!', 'success');

        } catch (error) {
            hideLoading();
            this.showError(errorDiv, error.message);
        }
    },

    // Show error in form
    showError(errorDiv, message) {
        errorDiv.innerHTML = message;
        errorDiv.classList.remove('hidden');
    },

    // Logout
    logout() {
        confirmDialog(
            'Are you sure you want to logout?',
            () => {
                ApiHelpers.logout();
                showToast('Logged out successfully', 'info');
            }
        );
    }
};

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Auth.init();
});
