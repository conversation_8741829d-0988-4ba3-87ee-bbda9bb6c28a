# MarkEase - Automated Answer Sheet Marking System

MarkEase is a web-based application designed to automate the marking process for student answer sheets. Teachers can upload answer sheets and marking guides, and the system will automatically scan, process, and mark the sheets, generating comprehensive reports.

## Features

- **Automated Scanning**: OCR-powered scanning of student answer sheets
- **Intelligent Marking**: Automated marking based on teacher-provided marking guides
- **Report Generation**: Comprehensive reports for individual students and class analytics
- **User Management**: Separate interfaces for teachers and administrators
- **Question Type Support**: Multiple choice, short answer, and essay questions

## Technology Stack

- **Frontend**: HTML, CSS, JavaScript, Tailwind CSS
- **Backend**: Python (Flask/FastAPI)
- **Database**: MySQL
- **Image Processing**: OpenCV, Tesseract OCR
- **Additional Libraries**: PIL, NumPy, pandas

## Project Structure

```
markease/
├── backend/                 # Python backend API
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API endpoints
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── config/             # Configuration files
│   ├── migrations/         # Database migrations
│   └── requirements.txt    # Python dependencies
├── frontend/               # Web frontend
│   ├── assets/            # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── pages/             # HTML pages
├── database/              # Database scripts
│   ├── schema.sql         # Database schema
│   └── seed_data.sql      # Sample data
└── docs/                  # Documentation
```

## Getting Started

### Prerequisites

- Python 3.8+
- MySQL 8.0+
- Node.js (for frontend build tools)

### Installation

1. Clone the repository
2. Set up the database using the schema in `database/schema.sql`
3. Install Python dependencies: `pip install -r backend/requirements.txt`
4. Configure environment variables
5. Run the backend server
6. Open the frontend in a web browser

## Usage

1. **Teacher Registration/Login**: Teachers create accounts and log in
2. **Upload Marking Guide**: Teachers upload or create marking guides for their tests
3. **Upload Answer Sheets**: Scan and upload student answer sheets
4. **Automated Processing**: System processes sheets using OCR and marking algorithms
5. **Review Results**: Teachers can review and adjust marks if needed
6. **Generate Reports**: Comprehensive reports are generated automatically

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License.
