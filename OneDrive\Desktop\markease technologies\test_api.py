#!/usr/bin/env python3
"""
Test API endpoints directly
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000/api"

def test_login():
    """Test login and get token."""
    url = f"{BASE_URL}/auth/login"
    data = {
        "username": "testuser",
        "password": "password123"
    }
    
    print("Testing login...")
    response = requests.post(url, json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        return result.get('access_token')
    return None

def test_marking_guides(token):
    """Test marking guides endpoint."""
    url = f"{BASE_URL}/marking-guides"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\nTesting GET marking guides...")
    response = requests.get(url, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

def test_create_guide(token):
    """Test create marking guide."""
    url = f"{BASE_URL}/marking-guides"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "title": "Test Guide",
        "subject": "Mathematics",
        "description": "Test description"
    }
    
    print("\nTesting POST create guide...")
    response = requests.post(url, json=data, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

def main():
    # Test login first
    token = test_login()
    
    if token:
        print(f"\nLogin successful! Token: {token[:50]}...")
        
        # Test marking guides endpoints
        test_marking_guides(token)
        test_create_guide(token)
    else:
        print("Login failed!")

if __name__ == "__main__":
    main()
