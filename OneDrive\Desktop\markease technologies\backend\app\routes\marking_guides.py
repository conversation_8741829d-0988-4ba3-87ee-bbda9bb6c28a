from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from app import db
from app.models.user import User
from app.models.marking_guide import MarkingGuide
from app.models.question import Question, QuestionType
import os
import uuid
import re

marking_guides_bp = Blueprint('marking_guides', __name__)

@marking_guides_bp.route('/test', methods=['GET'])
@jwt_required()
def test_route():
    """Simple test route."""
    try:
        user_id = int(get_jwt_identity())
        return jsonify({'message': 'Test successful', 'user_id': user_id}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('', methods=['GET'])
@marking_guides_bp.route('/', methods=['GET'])
@jwt_required()
def get_marking_guides():
    """Get all marking guides for the current teacher."""
    try:
        print("=== GET MARKING GUIDES DEBUG ===")
        user_id = int(get_jwt_identity())
        print(f"User ID: {user_id}")

        user = User.query.get(user_id)
        print(f"User found: {user}")

        if not user:
            print("User not found!")
            return jsonify({'error': 'User not found'}), 404
        
        # Get query parameters
        print("Getting query parameters...")
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        subject = request.args.get('subject')
        is_active = request.args.get('is_active', type=bool)
        print(f"Query params: page={page}, per_page={per_page}, subject={subject}, is_active={is_active}")
        
        # Build query
        query = MarkingGuide.query.filter_by(teacher_id=user_id)
        
        if subject:
            query = query.filter(MarkingGuide.subject.ilike(f'%{subject}%'))
        if is_active is not None:
            query = query.filter_by(is_active=is_active)
        
        # Order by creation date (newest first)
        query = query.order_by(MarkingGuide.created_at.desc())
        
        # Paginate
        marking_guides = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'marking_guides': [guide.to_dict() for guide in marking_guides.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': marking_guides.total,
                'pages': marking_guides.pages,
                'has_next': marking_guides.has_next,
                'has_prev': marking_guides.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('', methods=['POST'])
@marking_guides_bp.route('/', methods=['POST'])
@jwt_required()
def create_marking_guide():
    """Create a new marking guide."""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        
        # Validate required fields
        if not data.get('title') or not data.get('subject'):
            return jsonify({'error': 'Title and subject are required'}), 400
        
        # Create marking guide
        marking_guide = MarkingGuide(
            title=data['title'],
            subject=data['subject'],
            teacher_id=user_id,
            description=data.get('description'),
            grade_level=data.get('grade_level'),
            time_limit=data.get('time_limit'),
            instructions=data.get('instructions')
        )
        
        db.session.add(marking_guide)
        db.session.commit()
        
        return jsonify({
            'message': 'Marking guide created successfully',
            'marking_guide': marking_guide.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('/<int:guide_id>', methods=['GET'])
@jwt_required()
def get_marking_guide(guide_id):
    """Get a specific marking guide with its questions."""
    try:
        user_id = int(get_jwt_identity())

        marking_guide = MarkingGuide.query.filter_by(
            id=guide_id, teacher_id=user_id
        ).first()
        
        if not marking_guide:
            return jsonify({'error': 'Marking guide not found'}), 404
        
        # Get questions for this marking guide
        questions = Question.query.filter_by(
            marking_guide_id=guide_id
        ).order_by(Question.question_number).all()
        
        guide_dict = marking_guide.to_dict()
        guide_dict['questions'] = [question.to_dict() for question in questions]
        
        return jsonify({
            'marking_guide': guide_dict
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('/<int:guide_id>', methods=['PUT'])
@jwt_required()
def update_marking_guide(guide_id):
    """Update a marking guide."""
    try:
        user_id = int(get_jwt_identity())

        marking_guide = MarkingGuide.query.filter_by(
            id=guide_id, teacher_id=user_id
        ).first()
        
        if not marking_guide:
            return jsonify({'error': 'Marking guide not found'}), 404
        
        data = request.get_json()
        
        # Update fields
        if data.get('title'):
            marking_guide.title = data['title']
        if data.get('description') is not None:
            marking_guide.description = data['description']
        if data.get('subject'):
            marking_guide.subject = data['subject']
        if data.get('grade_level') is not None:
            marking_guide.grade_level = data['grade_level']
        if data.get('time_limit') is not None:
            marking_guide.time_limit = data['time_limit']
        if data.get('instructions') is not None:
            marking_guide.instructions = data['instructions']
        if data.get('is_active') is not None:
            marking_guide.is_active = data['is_active']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Marking guide updated successfully',
            'marking_guide': marking_guide.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('/<int:guide_id>', methods=['DELETE'])
@jwt_required()
def delete_marking_guide(guide_id):
    """Delete a marking guide."""
    try:
        user_id = int(get_jwt_identity())

        marking_guide = MarkingGuide.query.filter_by(
            id=guide_id, teacher_id=user_id
        ).first()
        
        if not marking_guide:
            return jsonify({'error': 'Marking guide not found'}), 404
        
        db.session.delete(marking_guide)
        db.session.commit()
        
        return jsonify({
            'message': 'Marking guide deleted successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('/<int:guide_id>/questions', methods=['POST'])
@jwt_required()
def add_question(guide_id):
    """Add a question to a marking guide."""
    try:
        user_id = int(get_jwt_identity())

        marking_guide = MarkingGuide.query.filter_by(
            id=guide_id, teacher_id=user_id
        ).first()
        
        if not marking_guide:
            return jsonify({'error': 'Marking guide not found'}), 404
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['question_number', 'question_text', 'question_type', 'marks']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Validate question type
        try:
            question_type = QuestionType(data['question_type'])
        except ValueError:
            return jsonify({'error': 'Invalid question type'}), 400
        
        # Check if question number already exists
        existing_question = Question.query.filter_by(
            marking_guide_id=guide_id,
            question_number=data['question_number']
        ).first()
        
        if existing_question:
            return jsonify({'error': 'Question number already exists'}), 400
        
        # Create question
        question = Question(
            question_number=data['question_number'],
            question_text=data['question_text'],
            question_type=question_type,
            marks=data['marks'],
            marking_guide_id=guide_id,
            correct_answer=data.get('correct_answer'),
            marking_criteria=data.get('marking_criteria')
        )
        
        # Set answer options for MCQ
        if data.get('answer_options'):
            question.set_answer_options(data['answer_options'])
        
        # Set keywords for automated marking
        if data.get('keywords'):
            question.set_keywords(data['keywords'])
        
        if data.get('case_sensitive') is not None:
            question.case_sensitive = data['case_sensitive']
        if data.get('partial_marks') is not None:
            question.partial_marks = data['partial_marks']
        
        db.session.add(question)
        
        # Update total marks for the marking guide
        marking_guide.calculate_total_marks()
        
        db.session.commit()
        
        return jsonify({
            'message': 'Question added successfully',
            'question': question.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@marking_guides_bp.route('/scan-answer-key', methods=['POST'])
@jwt_required()
def scan_answer_key():
    """Process scanned answer key to extract questions and answers."""
    try:
        user_id = int(get_jwt_identity())
        user = User.query.get(user_id)

        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Check if file is present
        if 'answer_key_file' not in request.files:
            return jsonify({'error': 'No answer key file provided'}), 400

        file = request.files['answer_key_file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Get form data
        title = request.form.get('title')
        subject = request.form.get('subject')

        if not title or not subject:
            return jsonify({'error': 'Title and subject are required'}), 400

        # Save the uploaded file temporarily
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        temp_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(temp_file_path)

        # Process the scanned answer key using OCR
        from app.services.ocr_service import OCRService
        ocr_service = OCRService(current_app.config.get('TESSERACT_CMD'))

        ocr_result = ocr_service.extract_text(temp_file_path)

        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        if ocr_result['processing_status'] == 'error':
            return jsonify({
                'error': 'Failed to process answer key image',
                'details': ocr_result.get('error_message', 'Unknown OCR error')
            }), 400

        # Parse the extracted text to identify questions and answers
        extracted_questions = parse_answer_key_text(ocr_result['full_text'])

        return jsonify({
            'message': 'Answer key processed successfully',
            'extracted_text': ocr_result['full_text'],
            'confidence': ocr_result['average_confidence'],
            'questions': extracted_questions,
            'suggestions': {
                'title': title,
                'subject': subject,
                'total_questions': len(extracted_questions),
                'estimated_marks': sum(q.get('marks', 1) for q in extracted_questions)
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def parse_answer_key_text(text):
    """Parse OCR text to extract questions and answers."""
    import re

    questions = []
    lines = text.split('\n')

    current_question = None
    question_pattern = r'^(\d+)[\.\)\s]+(.+)'

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Check if line starts with a question number
        match = re.match(question_pattern, line)
        if match:
            # Save previous question if exists
            if current_question:
                questions.append(current_question)

            question_num = int(match.group(1))
            question_text = match.group(2).strip()

            # Try to detect question type and extract answer
            question_type, answer = detect_question_type_and_answer(question_text)

            current_question = {
                'question_number': question_num,
                'question_text': question_text,
                'question_type': question_type,
                'correct_answer': answer,
                'marks': 1,  # Default marks
                'confidence': 0.8  # OCR confidence estimate
            }
        elif current_question:
            # This might be a continuation of the current question or answer
            current_question['question_text'] += ' ' + line

    # Add the last question
    if current_question:
        questions.append(current_question)

    return questions

def detect_question_type_and_answer(text):
    """Detect question type and extract answer from question text."""
    text_lower = text.lower()

    # Check for True/False questions
    if any(keyword in text_lower for keyword in ['true or false', 'true/false', 't/f']):
        # Look for answer indicators
        if 'true' in text_lower and 'false' not in text_lower:
            return 'true_false', 'True'
        elif 'false' in text_lower and 'true' not in text_lower:
            return 'true_false', 'False'
        else:
            return 'true_false', ''

    # Check for multiple choice (A, B, C, D patterns)
    mc_pattern = r'[A-D][\.\)]\s*([^A-D]+?)(?=[A-D][\.\)]|$)'
    mc_matches = re.findall(mc_pattern, text, re.IGNORECASE)
    if len(mc_matches) >= 2:
        # Look for answer indicator (circled, underlined, etc.)
        answer_pattern = r'answer[:\s]*([A-D])'
        answer_match = re.search(answer_pattern, text, re.IGNORECASE)
        if answer_match:
            return 'multiple_choice', answer_match.group(1).upper()
        else:
            return 'multiple_choice', ''

    # Check for short answer patterns
    if len(text.split()) <= 10:
        return 'short_answer', text

    # Default to essay for longer texts
    return 'essay', text
