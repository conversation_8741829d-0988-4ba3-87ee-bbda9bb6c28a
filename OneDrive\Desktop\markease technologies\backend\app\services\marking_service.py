import re
import difflib
from typing import Dict, List, Tuple, Optional
from app.models.question import Question, QuestionType
from app.models.answer import Answer
import logging

logger = logging.getLogger(__name__)

class MarkingService:
    """Service for automated marking of student answers."""
    
    def __init__(self):
        """Initialize marking service."""
        self.similarity_threshold = 0.8  # Minimum similarity for correct answers
        self.partial_credit_threshold = 0.5  # Minimum similarity for partial credit
    
    def normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        if not text:
            return ""
        
        # Convert to lowercase
        normalized = text.lower().strip()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Remove common punctuation (but keep essential ones)
        normalized = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', '', normalized)
        
        return normalized
    
    def calculate_similarity(self, answer1: str, answer2: str) -> float:
        """Calculate similarity between two text strings."""
        if not answer1 or not answer2:
            return 0.0
        
        # Normalize both texts
        norm1 = self.normalize_text(answer1)
        norm2 = self.normalize_text(answer2)
        
        # Use difflib for sequence matching
        similarity = difflib.SequenceMatcher(None, norm1, norm2).ratio()
        
        return similarity
    
    def mark_multiple_choice(self, question: Question, student_answer: str) -> Dict:
        """Mark multiple choice question."""
        try:
            if not question.correct_answer or not student_answer:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 0.0,
                    'feedback': 'No answer provided or correct answer not set'
                }
            
            # Normalize answers for comparison
            correct = self.normalize_text(question.correct_answer)
            student = self.normalize_text(student_answer)
            
            # Check for exact match or high similarity
            similarity = self.calculate_similarity(correct, student)
            
            if similarity >= self.similarity_threshold:
                return {
                    'marks_obtained': float(question.marks),
                    'is_correct': True,
                    'confidence': similarity,
                    'feedback': 'Correct answer'
                }
            else:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': similarity,
                    'feedback': f'Incorrect. Expected: {question.correct_answer}'
                }
                
        except Exception as e:
            logger.error(f"Error marking MCQ: {str(e)}")
            return {
                'marks_obtained': 0.0,
                'is_correct': False,
                'confidence': 0.0,
                'feedback': f'Error in marking: {str(e)}'
            }
    
    def mark_true_false(self, question: Question, student_answer: str) -> Dict:
        """Mark true/false question."""
        try:
            if not question.correct_answer or not student_answer:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 0.0,
                    'feedback': 'No answer provided or correct answer not set'
                }
            
            # Normalize and check for true/false variations
            student = self.normalize_text(student_answer)
            correct = self.normalize_text(question.correct_answer)
            
            # Define true/false variations
            true_variations = ['true', 't', 'yes', 'y', '1', 'correct']
            false_variations = ['false', 'f', 'no', 'n', '0', 'incorrect']
            
            # Determine student's answer
            student_bool = None
            if any(var in student for var in true_variations):
                student_bool = True
            elif any(var in student for var in false_variations):
                student_bool = False
            
            # Determine correct answer
            correct_bool = None
            if any(var in correct for var in true_variations):
                correct_bool = True
            elif any(var in correct for var in false_variations):
                correct_bool = False
            
            if student_bool is None:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 0.0,
                    'feedback': 'Could not determine True/False answer'
                }
            
            if student_bool == correct_bool:
                return {
                    'marks_obtained': float(question.marks),
                    'is_correct': True,
                    'confidence': 1.0,
                    'feedback': 'Correct answer'
                }
            else:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 1.0,
                    'feedback': f'Incorrect. Expected: {question.correct_answer}'
                }
                
        except Exception as e:
            logger.error(f"Error marking True/False: {str(e)}")
            return {
                'marks_obtained': 0.0,
                'is_correct': False,
                'confidence': 0.0,
                'feedback': f'Error in marking: {str(e)}'
            }
    
    def mark_short_answer(self, question: Question, student_answer: str) -> Dict:
        """Mark short answer question using keywords and similarity."""
        try:
            if not student_answer:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 0.0,
                    'feedback': 'No answer provided'
                }
            
            student = self.normalize_text(student_answer)
            total_marks = float(question.marks)
            marks_obtained = 0.0
            feedback_parts = []
            
            # Check against correct answer if provided
            if question.correct_answer:
                correct = self.normalize_text(question.correct_answer)
                similarity = self.calculate_similarity(correct, student)
                
                if similarity >= self.similarity_threshold:
                    marks_obtained = total_marks
                    feedback_parts.append("Matches expected answer closely")
                elif similarity >= self.partial_credit_threshold and question.partial_marks:
                    marks_obtained = total_marks * similarity
                    feedback_parts.append(f"Partial match with expected answer ({similarity:.2%})")
            
            # Check for keywords if provided
            keywords = question.get_keywords()
            if keywords:
                keyword_score = 0
                found_keywords = []
                
                for keyword in keywords:
                    keyword_norm = self.normalize_text(keyword)
                    if keyword_norm in student:
                        keyword_score += 1
                        found_keywords.append(keyword)
                
                if found_keywords:
                    keyword_marks = (keyword_score / len(keywords)) * total_marks
                    if keyword_marks > marks_obtained:
                        marks_obtained = keyword_marks
                        feedback_parts.append(f"Found keywords: {', '.join(found_keywords)}")
            
            # Ensure marks don't exceed maximum
            marks_obtained = min(marks_obtained, total_marks)
            
            is_correct = marks_obtained >= (total_marks * 0.7)  # 70% threshold for "correct"
            confidence = marks_obtained / total_marks if total_marks > 0 else 0
            
            feedback = '; '.join(feedback_parts) if feedback_parts else 'Answer evaluated'
            
            return {
                'marks_obtained': round(marks_obtained, 2),
                'is_correct': is_correct,
                'confidence': round(confidence, 2),
                'feedback': feedback
            }
            
        except Exception as e:
            logger.error(f"Error marking short answer: {str(e)}")
            return {
                'marks_obtained': 0.0,
                'is_correct': False,
                'confidence': 0.0,
                'feedback': f'Error in marking: {str(e)}'
            }
    
    def mark_essay(self, question: Question, student_answer: str) -> Dict:
        """Mark essay question (basic implementation)."""
        try:
            if not student_answer:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 0.0,
                    'feedback': 'No answer provided'
                }
            
            student = self.normalize_text(student_answer)
            total_marks = float(question.marks)
            marks_obtained = 0.0
            feedback_parts = []
            
            # Basic length check
            word_count = len(student.split())
            if word_count < 10:
                feedback_parts.append("Answer too short")
                marks_obtained = total_marks * 0.2  # 20% for minimal effort
            elif word_count < 50:
                feedback_parts.append("Answer could be more detailed")
                marks_obtained = total_marks * 0.5  # 50% for basic answer
            else:
                feedback_parts.append("Good length answer")
                marks_obtained = total_marks * 0.7  # 70% base for good length
            
            # Check for keywords if provided
            keywords = question.get_keywords()
            if keywords:
                keyword_score = 0
                found_keywords = []
                
                for keyword in keywords:
                    keyword_norm = self.normalize_text(keyword)
                    if keyword_norm in student:
                        keyword_score += 1
                        found_keywords.append(keyword)
                
                if found_keywords:
                    keyword_bonus = (keyword_score / len(keywords)) * (total_marks * 0.3)
                    marks_obtained += keyword_bonus
                    feedback_parts.append(f"Found key concepts: {', '.join(found_keywords)}")
            
            # Ensure marks don't exceed maximum
            marks_obtained = min(marks_obtained, total_marks)
            
            is_correct = marks_obtained >= (total_marks * 0.6)  # 60% threshold for essays
            confidence = 0.5  # Lower confidence for essay marking
            
            feedback = '; '.join(feedback_parts)
            feedback += " (Note: Essay requires manual review for accurate marking)"
            
            return {
                'marks_obtained': round(marks_obtained, 2),
                'is_correct': is_correct,
                'confidence': confidence,
                'feedback': feedback,
                'requires_manual_review': True
            }
            
        except Exception as e:
            logger.error(f"Error marking essay: {str(e)}")
            return {
                'marks_obtained': 0.0,
                'is_correct': False,
                'confidence': 0.0,
                'feedback': f'Error in marking: {str(e)}',
                'requires_manual_review': True
            }
    
    def mark_question(self, question: Question, student_answer: str) -> Dict:
        """Mark a question based on its type."""
        try:
            if question.question_type == QuestionType.MULTIPLE_CHOICE:
                return self.mark_multiple_choice(question, student_answer)
            elif question.question_type == QuestionType.TRUE_FALSE:
                return self.mark_true_false(question, student_answer)
            elif question.question_type == QuestionType.SHORT_ANSWER:
                return self.mark_short_answer(question, student_answer)
            elif question.question_type == QuestionType.ESSAY:
                return self.mark_essay(question, student_answer)
            else:
                return {
                    'marks_obtained': 0.0,
                    'is_correct': False,
                    'confidence': 0.0,
                    'feedback': f'Unknown question type: {question.question_type}'
                }
                
        except Exception as e:
            logger.error(f"Error marking question {question.id}: {str(e)}")
            return {
                'marks_obtained': 0.0,
                'is_correct': False,
                'confidence': 0.0,
                'feedback': f'Error in marking: {str(e)}'
            }
