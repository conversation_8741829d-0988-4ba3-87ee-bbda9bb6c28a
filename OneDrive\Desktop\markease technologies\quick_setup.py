#!/usr/bin/env python3
"""
MarkEase Quick Setup Script (SQLite version)
This script sets up MarkEase with SQLite for quick demonstration.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is 3.8 or higher."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def create_virtual_environment():
    """Create Python virtual environment."""
    venv_path = Path("venv")
    if venv_path.exists():
        print("✓ Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def install_python_dependencies():
    """Install Python dependencies."""
    if os.name == 'nt':  # Windows
        pip_command = "venv\\Scripts\\pip install -r backend\\requirements.txt"
    else:  # Unix/Linux/macOS
        pip_command = "venv/bin/pip install -r backend/requirements.txt"
    
    return run_command(pip_command, "Installing Python dependencies")

def create_env_file():
    """Create .env file with SQLite configuration."""
    env_content = """# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=demo-secret-key-change-in-production
JWT_SECRET_KEY=demo-jwt-secret-key-change-in-production

# Database Configuration (SQLite)
DEV_DATABASE_URL=sqlite:///markease_dev.db
TEST_DATABASE_URL=sqlite:///markease_test.db
DATABASE_URL=sqlite:///markease.db

# OCR Configuration (Optional - will use basic text processing if not available)
# TESSERACT_CMD=tesseract

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
"""
    
    env_file = "backend/.env"
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✓ Created {env_file}")

def create_uploads_directory():
    """Create uploads directory."""
    uploads_dir = Path("backend/uploads")
    uploads_dir.mkdir(exist_ok=True)
    print("✓ Created uploads directory")

def initialize_database():
    """Initialize the SQLite database."""
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_cmd = "venv/bin/python"
    
    # Change to backend directory and run init-db
    original_dir = os.getcwd()
    try:
        os.chdir("backend")
        return run_command(f"{os.path.join('..', python_cmd)} app.py init-db", "Initializing database")
    finally:
        os.chdir(original_dir)

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*50)
    print("QUICK SETUP COMPLETE!")
    print("="*50)
    
    print("\nThe system is ready to run with SQLite database.")
    print("Note: OCR functionality will be limited without Tesseract.")
    
    print("\nTo start the system:")
    print("1. Start the backend server:")
    if os.name == 'nt':  # Windows
        print("   cd backend")
        print("   ..\\venv\\Scripts\\python app.py")
    else:
        print("   cd backend")
        print("   ../venv/bin/python app.py")
    
    print("\n2. Open frontend/pages/index.html in your web browser")
    
    print("\n3. Register a new teacher account and start using MarkEase!")
    
    print("\nOptional: Install Tesseract OCR for full functionality:")
    print("- Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
    print("- macOS: brew install tesseract")
    print("- Ubuntu: sudo apt install tesseract-ocr")

def main():
    """Main setup function."""
    print("="*50)
    print("MARKEASE QUICK SETUP (SQLite)")
    print("="*50)
    
    # Check prerequisites
    print("\nChecking prerequisites...")
    if not check_python_version():
        sys.exit(1)
    
    # Setup Python environment
    print("\nSetting up Python environment...")
    if not create_virtual_environment():
        sys.exit(1)
    
    if not install_python_dependencies():
        sys.exit(1)
    
    # Create configuration files
    print("\nCreating configuration files...")
    create_env_file()
    create_uploads_directory()
    
    # Initialize database
    print("\nSetting up database...")
    if not initialize_database():
        print("⚠️  Database initialization failed, but you can try running it manually later")
    
    # Print next steps
    print_next_steps()

if __name__ == "__main__":
    main()
